'use client';

import { BaseButton } from '@/components/atoms/button';
import { BaseLabel } from '@/components/atoms/label';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import { BaseSeparator } from '@/components/atoms/separator';
import { cn } from '@/utils/common';
import { useShallow } from 'zustand/react/shallow';
import { useState } from 'react';
import { useLearningHistoryReportFilterStore } from '@/store/admin/online-learning/learning-history-report/filter';

interface IFilter {
  sectionType: string | undefined;
  dateRange: { from: Date | null; to: Date | null };
}

const LearningHistoryReportTableHeaderFilterInput = () => {
  const { openFilter, query, setQuery } = useLearningHistoryReportFilterStore(
    useShallow(({ openFilter, query, setQuery }) => ({
      openFilter,
      query,
      setQuery,
    }))
  );

  const [filter, setFilter] = useState<IFilter>({
    sectionType: undefined,
    dateRange: { from: null, to: null },
  });

  const handleChange = (key: keyof IFilter, value: any) => {
    setFilter((prev) => ({ ...prev, [key]: value }));
  };

  const handleSubmit = () => {
    setQuery({
      ...query,
      sectionType: filter.sectionType,
      dateRange: filter.dateRange,
      page: 1,
    });
  };

  const handleReset = () => {
    setFilter({
      sectionType: undefined,
      dateRange: { from: null, to: null },
    });
    setQuery({
      ...query,
      sectionType: undefined,
      dateRange: undefined,
      page: 1,
    });
  };

  return (
    <div
      hidden={!openFilter}
      className={cn('flex flex-col gap-4 w-full bg-white rounded-lg p-3')}
    >
      <div className="flex items-center justify-between">
        <span className="mt-1 font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            className="w-24 h-10 text-red-600 border-red-600 hover:text-red-600"
            variant={'outline'}
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton
            className="w-24 h-10"
            onClick={handleSubmit}
          >
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="flex gap-4 justify-between">
        {/* Section Type */}
        <div className="flex flex-col gap-1 w-1/2">
          <BaseLabel className="text-xs">Date Range (Issued Date)</BaseLabel>
          <BaseSelect
            value={filter.sectionType ?? ''}
            onValueChange={(value) => handleChange('sectionType', value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select date range" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="a">A</BaseSelectItem>
              <BaseSelectItem value="b">B</BaseSelectItem>
              <BaseSelectItem value="c">C</BaseSelectItem>
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Date Range */}
        <div className="flex flex-col gap-1 w-1/2">
          <BaseLabel className="text-xs">Date Range (Completed Date)</BaseLabel>
          <BaseSelect
            value={filter.sectionType ?? ''}
            onValueChange={(value) => handleChange('sectionType', value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select date range" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="a">A</BaseSelectItem>
              <BaseSelectItem value="b">B</BaseSelectItem>
              <BaseSelectItem value="c">C</BaseSelectItem>
            </BaseSelectContent>
          </BaseSelect>
        </div>
      </div>
    </div>
  );
};

export default LearningHistoryReportTableHeaderFilterInput;
