"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLef<PERSON> } from "lucide-react";
import { useRouter } from "next/navigation";
import { BaseButton } from "@/components/atoms/button";
import SectionInformation from "@/components/molecules/admin/manage-section/add/section-information";
import SectionPreview from "@/components/molecules/admin/manage-section/add/section-preview";
import RelatedCompetencies from "@/components/molecules/admin/manage-section/add/related-competencies";
import SectionDuration from "@/components/molecules/admin/manage-section/add/section-duration";

// Form schema
const addSectionSchema = z.object({
  sectionName: z.string().min(1, "Section name is required"),
  sectionId: z.string().min(1, "Section ID is required"),
  sectionType: z.enum(["video", "audio", "document", "quiz"]),
  materialId: z.string().optional(),
  passingGrade: z.number().min(0).max(100).optional(),
  numberOfQuestions: z.number().min(1).optional(),
  technicalCompetencies: z.array(z.string()).optional(),
  softCompetencies: z.array(z.string()).optional(),
  hours: z.number().min(0).optional(),
  minutes: z.number().min(0).max(59).optional(),
  seconds: z.number().min(0).max(59).optional(),
});

export type AddSectionFormData = z.infer<typeof addSectionSchema>;

const AddNewSection = () => {
  const router = useRouter();

  const form = useForm<AddSectionFormData>({
    resolver: zodResolver(addSectionSchema),
    defaultValues: {
      sectionName: "",
      sectionId: "",
      sectionType: "video",
      materialId: "",
      passingGrade: 70,
      numberOfQuestions: 10,
      technicalCompetencies: [],
      softCompetencies: [],
      hours: 0,
      minutes: 0,
      seconds: 0,
    },
  });

  const {
    handleSubmit,
    watch,
    control,
    formState: { errors },
  } = form;
  const sectionType = watch("sectionType");

  const onSubmit = (data: AddSectionFormData) => {
    console.log("Form submitted:", data);
    // Handle form submission here
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="min-h-screen">
      <div>
        {/* Header */}
        <div className="flex items-center gap-4 mb-5">
          <button
            onClick={handleCancel}
            className="flex items-center justify-center w-10 h-10 rounded-lg bg-white"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </button>
          <h1 className="text-base font-bold text-gray-900 bg-white p-3 rounded-lg w-full">
            Add New Section
          </h1>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-5 bg-white p-5 rounded-lg">
            {/* Section Information */}
            <SectionInformation control={control} errors={errors} />
            {/* Section Preview */}
            <SectionPreview
              sectionType={sectionType}
              control={control}
              errors={errors}
            />
            {/* Related Competencies */}
            <RelatedCompetencies control={control} errors={errors} />
            {/* Section Duration */}
            <SectionDuration control={control} errors={errors} />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 py-4 bg-white mt-4 -mx-8 w-[calc(100%+48px)]">
            <BaseButton
              type="button"
              variant="outline"
              onClick={handleCancel}
              className="px-4 py-3 text-sm"
            >
              Cancel
            </BaseButton>
            <BaseButton
              type="submit"
              className="px-4 py-3 text-sm bg-fill-brand-primary"
            >
              Add New Section
            </BaseButton>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddNewSection;
