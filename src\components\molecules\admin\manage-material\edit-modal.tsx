import { BaseButton } from "@/components/atoms/button";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseInput } from "@/components/atoms/input";
import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { DialogClose } from "@/components/ui/dialog";
import {
  IGetListMaterialResponse,
  IMaterial,
} from "@/interfaces/admin/manage-material/list";
import React from "react";
import { useForm, FormProvider } from "react-hook-form";
import { X, Trash2, ArrowRight, ArrowLeft } from "lucide-react";

// Types
type MaterialType = "video" | "audio" | "document";

interface AssociatedSection {
  id: number;
  section: string;
  category: string;
  level: string;
}

interface EditFormData {
  id: string;
  name: string;
  categories: string[];
  level: string;
  associatedSections: AssociatedSection[];
}

// Dummy data
const DUMMY_CATEGORIES = [
  { id: "1", name: "DI Yogyakarta" },
  { id: "2", name: "Bali" },
  { id: "3", name: "Kepulauan Bangka Belitung" },
  { id: "4", name: "Jawa Barat" },
  { id: "5", name: "Jawa Tengah" },
  { id: "6", name: "Jawa Timur" },
];

const DUMMY_LEVELS = [
  { id: "beginner", name: "Beginner" },
  { id: "intermediate", name: "Intermediate" },
  { id: "advanced", name: "Advanced" },
];

const DUMMY_ASSOCIATED_SECTIONS: AssociatedSection[] = [
  {
    id: 1,
    section: "Mengenal Berbagi Tipologi Customer",
    category: "2 Category",
    level: "4 Levels",
  },
  {
    id: 2,
    section: "Materi REMO Basic Knowledge",
    category: "2 Category",
    level: "4 Levels",
  },
  {
    id: 3,
    section: "Strategi Penagihan",
    category: "2 Category",
    level: "4 Levels",
  },
];

const FILE_CONFIGS = {
  video: {
    title: "Edit Video",
    fileLabel: "Video",
    icon: "🎥",
  },
  audio: {
    title: "Edit Audio",
    fileLabel: "Audio",
    icon: "🎵",
  },
  document: {
    title: "Edit Document",
    fileLabel: "Document",
    icon: "📄",
  },
};

// Main Modal Component
interface EditMaterialModalProps {
  isOpen: boolean;
  onClose: () => void;
  material: IGetListMaterialResponse | null;
  type: MaterialType;
}

const EditMaterialModal = ({
  isOpen,
  onClose,
  material,
  type,
}: EditMaterialModalProps) => {
  const config = FILE_CONFIGS[type];

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="w-full max-w-[600px] sm:max-w-[808px] max-h-[90vh] overflow-y-auto">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex items-center justify-between">
            <span>{config.title}</span>
          </BaseDialogTitle>
        </BaseDialogHeader>
        <EditMaterialForm material={material} type={type} onClose={onClose} />
      </BaseDialogContent>
    </BaseDialog>
  );
};
// Form Component
interface EditMaterialFormProps {
  material: IGetListMaterialResponse | null;
  type: MaterialType;
  onClose: () => void;
}

const EditMaterialForm = ({
  material,
  type,
  onClose,
}: EditMaterialFormProps) => {
  const form = useForm<EditFormData>({
    defaultValues: {
      id: material?.id.toString() || "",
      name: material?.name || "",
      categories: [],
      level: "",
      associatedSections: DUMMY_ASSOCIATED_SECTIONS,
    },
  });

  const [selectedCategories, setSelectedCategories] = React.useState<string[]>([
    "4",
    "5",
    "6", // Jawa Barat, Jawa Tengah, Jawa Timur as shown in design
  ]);
  const [associatedSections, setAssociatedSections] = React.useState<
    AssociatedSection[]
  >(DUMMY_ASSOCIATED_SECTIONS);
  const [currentPage, setCurrentPage] = React.useState(1);
  const sectionsPerPage = 3;

  const config = FILE_CONFIGS[type];

  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleSelectAllCategories = () => {
    if (selectedCategories.length === DUMMY_CATEGORIES.length) {
      setSelectedCategories([]);
    } else {
      setSelectedCategories(DUMMY_CATEGORIES.map((cat) => cat.id));
    }
  };

  const onSubmit = (data: EditFormData) => {
    const submitData = {
      ...data,
      categories: selectedCategories,
      associatedSections,
    };

    console.log("Edit form submitted:", submitData);
    onClose();
  };

  const totalSections = associatedSections.length;
  const totalPages = Math.ceil(totalSections / sectionsPerPage);
  const startIndex = (currentPage - 1) * sectionsPerPage;
  const endIndex = startIndex + sectionsPerPage;
  const currentSections = associatedSections.slice(startIndex, endIndex);

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Video Information Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">
            {config.fileLabel} Information
          </h3>

          <div className="grid grid-cols-2 gap-4">
            {/* Video ID */}
            <div className="space-y-2">
              <BaseLabel className="text-sm font-medium">
                {config.fileLabel} ID
              </BaseLabel>
              <BaseInput
                value={form.watch("id")}
                readOnly
                disabled
                className="bg-gray-100"
              />
            </div>

            {/* Video Name */}
            <div className="space-y-2">
              <BaseLabel className="text-sm font-medium">
                {config.fileLabel} Name
              </BaseLabel>
              <BaseInput {...form.register("name")} placeholder="Enter name" />
            </div>
          </div>

          {/* Category Selection - Reused Component */}
          <CategorySelector
            selectedCategories={selectedCategories}
            onCategoryToggle={handleCategoryToggle}
            onSelectAll={handleSelectAllCategories}
          />

          {/* Level Selection - Reused Component */}
          <LevelSelector form={form} />
        </div>

        {/* Uploaded File Display */}
        <div className="space-y-3">
          <BaseLabel className="text-sm font-medium">
            Uploaded {config.fileLabel}
          </BaseLabel>
          <div className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-purple-100 rounded flex items-center justify-center">
                <span className="text-purple-600 text-lg">{config.icon}</span>
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900">
                Delta Introduction.mp4
              </p>
              <p className="text-xs text-gray-500">22 Jun, 2022 • 232 KB</p>
            </div>

            <button
              type="button"
              className="p-1 text-red-500 hover:bg-red-50 rounded"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Associated Section Table */}
        <AssociatedSectionTable
          sections={currentSections}
          onSectionUpdate={(id, field, value) => {
            setAssociatedSections((prev) =>
              prev.map((section) =>
                section.id === id ? { ...section, [field]: value } : section
              )
            );
          }}
        />

        {/* Pagination for Associated Sections */}
        {totalPages > 1 && (
          <SectionPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <DialogClose asChild>
            <BaseButton variant="outline" className="px-8">
              Cancel
            </BaseButton>
          </DialogClose>
          <BaseButton
            type="submit"
            className="px-8 bg-orange-500 hover:bg-orange-600"
          >
            Save Changes
          </BaseButton>
        </div>
      </form>
    </FormProvider>
  );
};
// Reused Category Selector Component
interface CategorySelectorProps {
  selectedCategories: string[];
  onCategoryToggle: (categoryId: string) => void;
  onSelectAll: () => void;
}

const CategorySelector = ({
  selectedCategories,
  onCategoryToggle,
  onSelectAll,
}: CategorySelectorProps) => {
  const isAllSelected = selectedCategories.length === DUMMY_CATEGORIES.length;

  return (
    <div className="space-y-3">
      <BaseLabel className="text-sm font-medium">Category</BaseLabel>

      {/* Selected Categories Display */}
      <div className="min-h-[60px] p-3 border rounded-lg bg-gray-50 flex flex-wrap gap-2 items-center">
        {selectedCategories.map((categoryId) => {
          const category = DUMMY_CATEGORIES.find(
            (cat) => cat.id === categoryId
          );
          return (
            <span
              key={categoryId}
              className="inline-flex items-center gap-1 px-3 py-1 bg-white border rounded-full text-sm"
            >
              {category?.name}
              <button
                type="button"
                onClick={() => onCategoryToggle(categoryId)}
                className="ml-1 hover:bg-gray-100 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          );
        })}
        {selectedCategories.length === 0 && (
          <span className="text-gray-400 text-sm">Select categories...</span>
        )}
      </div>

      {/* Select All Checkbox */}
      <label className="flex items-center gap-2 cursor-pointer">
        <input
          type="checkbox"
          checked={isAllSelected}
          onChange={onSelectAll}
          className="w-4 h-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500"
        />
        <span className="text-sm text-gray-700">Select All Category</span>
      </label>
    </div>
  );
};

// Reused Level Selector Component
interface LevelSelectorProps {
  form: any;
}

const LevelSelector = ({ form }: LevelSelectorProps) => {
  return (
    <div className="space-y-3">
      <BaseLabel className="text-sm font-medium">Level</BaseLabel>
      <BaseSelect
        value={form.watch("level")}
        onValueChange={(value) => form.setValue("level", value)}
      >
        <BaseSelectTrigger className="w-full h-12">
          <BaseSelectValue placeholder="Select Level" />
        </BaseSelectTrigger>
        <BaseSelectContent>
          {DUMMY_LEVELS.map((level) => (
            <BaseSelectItem key={level.id} value={level.id}>
              {level.name}
            </BaseSelectItem>
          ))}
        </BaseSelectContent>
      </BaseSelect>
    </div>
  );
};
// Associated Section Table Component
interface AssociatedSectionTableProps {
  sections: AssociatedSection[];
  onSectionUpdate: (id: number, field: string, value: string) => void;
}

const AssociatedSectionTable = ({
  sections,
  onSectionUpdate,
}: AssociatedSectionTableProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Associated Section</h3>

      <div className="border rounded-lg overflow-hidden">
        {/* Table Header */}
        <div className="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b font-medium text-sm text-gray-700">
          <div className="col-span-1">ID</div>
          <div className="col-span-5">Section</div>
          <div className="col-span-3">Category</div>
          <div className="col-span-3">Level</div>
        </div>

        {/* Table Body */}
        <div className="divide-y">
          {sections.map((section) => (
            <div
              key={section.id}
              className="grid grid-cols-12 gap-4 p-4 items-center"
            >
              {/* ID */}
              <div className="col-span-1 text-sm text-gray-900">
                {section.id}
              </div>

              {/* Section Name */}
              <div className="col-span-5 text-sm text-gray-900">
                {section.section}
              </div>

              {/* Category Dropdown */}
              <div className="col-span-3">
                <BaseSelect
                  value={section.category}
                  onValueChange={(value) =>
                    onSectionUpdate(section.id, "category", value)
                  }
                >
                  <BaseSelectTrigger className="w-full !h-6 cursor-pointer bg-base-gray-40 px-2 py-1 text-xs text-comp-content-primary max-w-max rounded-full">
                    <BaseSelectValue />
                  </BaseSelectTrigger>
                  <BaseSelectContent>
                    <BaseSelectItem value="1 Category">
                      1 Category
                    </BaseSelectItem>
                    <BaseSelectItem value="2 Category">
                      2 Category
                    </BaseSelectItem>
                    <BaseSelectItem value="3 Category">
                      3 Category
                    </BaseSelectItem>
                  </BaseSelectContent>
                </BaseSelect>
              </div>

              {/* Level Dropdown */}
              <div className="col-span-3">
                <BaseSelect
                  value={section.level}
                  onValueChange={(value) =>
                    onSectionUpdate(section.id, "level", value)
                  }
                >
                  <BaseSelectTrigger className="w-full !h-6 cursor-pointer bg-base-gray-40 px-2 py-1 text-xs text-comp-content-primary max-w-max rounded-full">
                    <BaseSelectValue />
                  </BaseSelectTrigger>
                  <BaseSelectContent>
                    <BaseSelectItem value="2 Levels">2 Levels</BaseSelectItem>
                    <BaseSelectItem value="3 Levels">3 Levels</BaseSelectItem>
                    <BaseSelectItem value="4 Levels">4 Levels</BaseSelectItem>
                    <BaseSelectItem value="5 Levels">5 Levels</BaseSelectItem>
                  </BaseSelectContent>
                </BaseSelect>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Section Pagination Component
interface SectionPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const SectionPagination = ({
  currentPage,
  totalPages,
  onPageChange,
}: SectionPaginationProps) => {
  return (
    <div className="flex items-center justify-center gap-1">
      <button
        onClick={() => onPageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
      >
        <ArrowLeft />
      </button>

      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
        const page = i + 1;
        return (
          <button
            key={page}
            onClick={() => onPageChange(page)}
            className={`px-3 py-1 rounded ${
              page === currentPage
                ? "bg-gray-200 text-gray-900"
                : "text-gray-600 hover:bg-gray-100"
            }`}
          >
            {page}
          </button>
        );
      })}

      {totalPages > 5 && (
        <>
          <span className="px-2 text-gray-400">...</span>
          <button
            onClick={() => onPageChange(totalPages)}
            className="px-3 py-1 text-gray-600 hover:bg-gray-100 rounded"
          >
            {totalPages}
          </button>
        </>
      )}

      <button
        onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
      >
        <ArrowRight />
      </button>
    </div>
  );
};

export default EditMaterialModal;
