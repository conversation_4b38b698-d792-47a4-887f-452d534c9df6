'use client';

import { useMemo, useState } from 'react';
import { DataTable } from '@/components/molecules/global/table';
import { getColumnsAssignmentAndTestResultReport } from './columns';
import AssignmentAndTestResultReportTableHeader from './table-header';

const baseItem = {
  userId: '12345',
  npk: '12345',
  fullName: 'Nama Learner',
  module: 'Module NEOP Contoh A',
  subModule: 'Sub Module Post Test',
  section: 'Section A Post Test',
  sectionType: 'Post-test Pilihan Ganda',
  score: 90,
};

const dummyData = Array.from({ length: 20 }).map((_, i) => ({
  ...baseItem,
  userId: String(10000 + i),
  npk: String(10000 + i),
  fullName: `Nama <PERSON> ${i + 1}`,
  section: `Section A Post Test #${i + 1}`,
}));

const PAGE_SIZE = 10;

const AssignmentAndTestResultReportTable = () => {
  const [page, setPage] = useState(1);

  const columns = useMemo(() => getColumnsAssignmentAndTestResultReport(), []);

  const pagedData = useMemo(() => {
    const start = (page - 1) * PAGE_SIZE;
    return dummyData.slice(start, start + PAGE_SIZE);
  }, [page]);

  const totalPage = Math.ceil(dummyData.length / PAGE_SIZE);

  return (
    <div className="flex flex-col gap-4">
      <AssignmentAndTestResultReportTableHeader />

      <DataTable
        containerClassName="max-h-[calc(100vh-280px)] overflow-y-auto"
        columns={columns}
        data={pagedData}
        pagination={{
          current_page: page,
          total_page: totalPage,
          total_data: dummyData.length,
          next: page < totalPage ? String(page + 1) : null,
          prev: page > 1 ? String(page - 1) : null,
        }}
        onPageChange={setPage}
      />
    </div>
  );
};

export default AssignmentAndTestResultReportTable;
