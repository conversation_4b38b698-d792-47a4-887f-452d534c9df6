"use client";

import FilterButton from "@/components/atoms/filter-button";
import { RefreshCwIcon } from "lucide-react";

interface Props {
  filterOpen: boolean;
  setFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const QuestionBankTableHeaderFilter = ({
  filterOpen,
  setFilterOpen,
}: Readonly<Props>) => {
  return (
    <div className="flex items-center justify-end gap-3">
      <RefreshCwIcon className="w-6 h-6 cursor-pointer" />
      <FilterButton
        active={filterOpen}
        onClick={() => setFilterOpen((prev) => !prev)}
        className="h-12"
      />
    </div>
  );
};

export default QuestionBankTableHeaderFilter;
