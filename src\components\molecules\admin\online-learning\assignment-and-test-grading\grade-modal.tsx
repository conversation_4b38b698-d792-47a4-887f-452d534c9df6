'use client';

import { BaseButton } from '@/components/atoms/button';
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { BaseInput } from '@/components/atoms/input';
import { BaseLabel } from '@/components/atoms/label';
import { BaseSeparator } from '@/components/atoms/separator';
import { BaseTextarea } from '@/components/atoms/textarea';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import { DialogClose } from '@/components/ui/dialog';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { useEffect, useMemo, useState } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { cn } from '@/lib/utils';
import { BookCopy, FileIcon } from 'lucide-react';
import { useAssignmentAndTestGradingModal } from '@/store/admin/online-learning/assignment-and-test-grading/modal';

type SubmissionMode = 'link' | 'document' | 'questions';

interface IQuestionItem {
  id: string;
  question: string;
  keyAnswer: string;
  learnerAnswer?: string;
  score?: string;
}

interface IAssignmentForm {
  npk: string;
  fullName: string;
  sectionType: string;
  moduleId: string;
  subModuleId: string;
  sectionId: string;
  instruction: string;
  submissionLink: string;
  passingGrade: string;
  sectionAttempt: string;
  score: string;
  retake: string;
  questions: IQuestionItem[];
}

const DUMMY_QUESTIONS: IQuestionItem[] = Array.from({ length: 15 }).map(
  (_, i) => ({
    id: String(i + 1),
    question:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
    keyAnswer: ['A', 'B', 'C', 'D'][i % 4],
    learnerAnswer: ['A', 'B', 'C', 'D'][(i + 1) % 4],
    score: '',
  })
);

const AssignmentAndTestGradingModal = () => {
  const { openModal, setOpenModal, openedId, setOpenedId } =
    useAssignmentAndTestGradingModal(
      useShallow((state) => ({
        openModal: state.openModal,
        setOpenModal: state.setOpenModal,
        openedId: state.openedId,
        setOpenedId: state.setOpenedId,
      }))
    );

  const form = useForm<IAssignmentForm>({
    defaultValues: {
      npk: '12345',
      fullName: 'Jonathan James Doe',
      sectionType: 'Post-test',
      moduleId: 'Module ID',
      subModuleId: 'Sub Module ID',
      sectionId: 'Section ID',
      instruction: 'Assignment Instruction',
      submissionLink: 'https://example.com/submission',
      passingGrade: '70',
      sectionAttempt: '1',
      score: '',
      retake: 'yes',
      questions: DUMMY_QUESTIONS,
    },
  });

  const sectionType = useWatch({ control: form.control, name: 'sectionType' });

  const [mode, setMode] = useState<SubmissionMode>('link');
  const isQuestions = mode === 'questions';

  useEffect(() => {
    const st = (sectionType || '').toLowerCase();
    if (
      st.includes('post-test') ||
      st.includes('pre-test') ||
      st.includes('test')
    ) {
      setMode('questions');
    }
  }, [sectionType]);

  const questions =
    useWatch({ control: form.control, name: 'questions' }) || [];
  const [qIndex, setQIndex] = useState(0);
  const currentQ = useMemo(() => questions[qIndex], [questions, qIndex]);

  const goPrev = () => setQIndex((i) => Math.max(0, i - 1));
  const goNext = () => setQIndex((i) => Math.min(questions.length - 1, i + 1));

  const onSubmit = (data: IAssignmentForm) => {
    console.log('Submitted Score:', data);
    setOpenModal(false);
  };

  useEffect(() => {
    if (!openModal) {
      form.reset();
      setOpenedId('');
      setMode('link');
      setQIndex(0);
    }
  }, [openModal, form, setOpenedId]);

  const handleCopy = async () => {
    const value = form.getValues('submissionLink');
    if (value) await navigator.clipboard.writeText(value);
  };

  return (
    <BaseDialog
      open={openModal}
      onOpenChange={setOpenModal}
    >
      <BaseDialogContent className="max-w-[808px]! max-h-[90vh] overflow-x-auto scrollbar-hide p-0 gap-0">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4 px-5 py-[18px]">
            <p className="text-[#3C3C3C] font-medium">
              Assignment and Test Grading
            </p>
          </BaseDialogTitle>
        </BaseDialogHeader>

        <BaseSeparator />

        <FormProvider {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-6 text-sm pt-5"
          >
            {/* Assignment and Test Information */}
            <div className="space-y-4 px-5">
              <h3 className="text-[#3C3C3C] text-sm font-semibold">
                Assignment and Test Information
              </h3>
              <div className="grid grid-cols-3 gap-4">
                {[
                  { id: 'npk', label: 'NPK' },
                  { id: 'fullName', label: 'Full Name' },
                  { id: 'sectionType', label: 'Section Type' },
                  { id: 'moduleId', label: 'Module ID' },
                  { id: 'subModuleId', label: 'Sub Module ID' },
                  { id: 'sectionId', label: 'Section ID' },
                ].map((field) => (
                  <div
                    key={field.id}
                    className="flex flex-col"
                  >
                    <BaseLabel
                      htmlFor={field.id}
                      className="text-xs font-medium text-[#3C3C3C] mb-1"
                    >
                      {field.label}
                    </BaseLabel>
                    <BaseInput
                      id={field.id}
                      type="text"
                      disabled
                      className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
                      {...form.register(field.id as keyof IAssignmentForm)}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Assignment and Test Submission */}
            <div className="space-y-4 px-5">
              <div className="flex items-center justify-between">
                <h3 className="text-[#3C3C3C] text-sm font-semibold">
                  Assignment and Test Submission
                </h3>

                {/* Trigger (segmented control) */}
                <div className="inline-flex rounded-md border border-[#DEDEDE] overflow-hidden">
                  {(['link', 'document', 'questions'] as SubmissionMode[]).map(
                    (m) => (
                      <button
                        key={m}
                        type="button"
                        onClick={() => setMode(m)}
                        className={cn(
                          'px-3 py-1 text-xs font-medium',
                          mode === m
                            ? 'bg-[#F1F1F1] text-[#3C3C3C]'
                            : 'bg-white text-[#767676]'
                        )}
                        aria-pressed={mode === m}
                      >
                        {m === 'link'
                          ? 'Link'
                          : m === 'document'
                          ? 'Document'
                          : 'Questions'}
                      </button>
                    )
                  )}
                </div>
              </div>

              {/* Instruction (selalu tampil) */}
              <BaseLabel
                htmlFor="instruction"
                className="text-xs font-medium text-[#3C3C3C] mb-1"
              >
                Assignment Instruction
              </BaseLabel>
              <BaseTextarea
                id="instruction"
                disabled
                maxLength={100}
                showCount
                className={cn(
                  'w-full min-h-[100px] bg-[#FFFFFF] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none resize-none'
                )}
                {...form.register('instruction')}
              />

              {/* Mode: LINK (Gambar 1) */}
              {mode === 'link' && (
                <div className="flex flex-col">
                  <BaseLabel
                    htmlFor="submissionLink"
                    className="text-xs font-medium text-[#3C3C3C] mb-1"
                  >
                    Submission Link
                  </BaseLabel>
                  <div className="flex gap-5">
                    <BaseInput
                      id="submissionLink"
                      type="text"
                      disabled
                      className="flex-1 bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
                      {...form.register('submissionLink')}
                    />
                    <BaseButton
                      type="button"
                      variant="outline"
                      className="px-3 text-[#3C3C3C] flex flex-row gap-1 items-center"
                      onClick={handleCopy}
                    >
                      <BookCopy size={16} />
                      <span className="text-xs font-medium">Copy Link</span>
                    </BaseButton>
                  </div>
                </div>
              )}

              {/* Mode: DOCUMENT (Gambar 2) */}
              {mode === 'document' && (
                <div className="flex flex-col">
                  <BaseLabel className="text-xs font-medium text-[#3C3C3C] mb-1">
                    Submission Document
                  </BaseLabel>
                  <div className="w-full border border-[#DEDEDE] rounded-md p-3 bg-white flex items-center gap-3">
                    <FileIcon
                      size={48}
                      color="#FFFFFF"
                      fill="#767676"
                    />
                    <div className="flex-1">
                      <BaseButton
                        type="button"
                        variant="link"
                        className="text-sm text-[#F7941E] p-0 h-fit"
                      >
                        Document1.pdf
                      </BaseButton>
                      <p className="text-[10px] text-[#767676]">
                        22 Jun, 2022 · 232 KB
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Mode: QUESTIONS (Gambar 3) */}
              {mode === 'questions' && currentQ && (
                <div className="w-full border border-[#DEDEDE] rounded-md p-4 bg-white space-y-3">
                  <div className="flex items-center justify-between">
                    <p className="font-semibold text-[#3C3C3C]">
                      Question ({qIndex + 1} dari {questions.length})
                    </p>
                  </div>

                  <p className="text-[#6B6B6B] leading-relaxed">
                    {currentQ.question}
                  </p>

                  <div className="flex flex-col gap-3">
                    <div className="flex flex-col">
                      <BaseLabel className="text-xs font-medium text-[#3C3C3C] mb-1">
                        Learners Answer
                      </BaseLabel>
                      <BaseInput
                        value={currentQ.learnerAnswer ?? ''}
                        onChange={(e) =>
                          form.setValue(
                            `questions.${qIndex}.learnerAnswer`,
                            e.target.value,
                            { shouldDirty: true }
                          )
                        }
                        disabled
                        className="bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
                      />
                    </div>

                    <div className="flex flex-col">
                      <BaseLabel className="text-xs font-medium text-[#3C3C3C] mb-1">
                        Key Answer
                      </BaseLabel>
                      <BaseInput
                        value={currentQ.keyAnswer}
                        disabled
                        className="bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
                      />
                    </div>

                    <div className="flex flex-col">
                      <BaseLabel className="text-xs font-medium text-[#3C3C3C] mb-1">
                        Score
                      </BaseLabel>
                      <BaseInput
                        placeholder="Input score"
                        value={currentQ.score ?? ''}
                        onChange={(e) =>
                          form.setValue(
                            `questions.${qIndex}.score`,
                            e.target.value,
                            { shouldDirty: true }
                          )
                        }
                        className="border border-[#DEDEDE] text-sm text-[#3C3C3C] rounded-md shadow-none"
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-1">
                    <BaseButton
                      type="button"
                      variant="outline"
                      onClick={goPrev}
                      disabled={qIndex === 0}
                    >
                      ← Previous
                    </BaseButton>
                    <BaseButton
                      type="button"
                      variant="outline"
                      onClick={goNext}
                      disabled={qIndex === questions.length - 1}
                    >
                      Next →
                    </BaseButton>
                  </div>
                </div>
              )}
            </div>

            {/* Assignment and Test Grading */}
            <div className="space-y-4 px-5">
              <h3 className="text-[#3C3C3C] text-sm font-semibold">
                Assignment and Test Grading
              </h3>

              <div className="grid grid-cols-2 gap-5">
                {[
                  { id: 'passingGrade', label: 'Passing Grade' },
                  { id: 'sectionAttempt', label: 'Section Attempt' },
                ].map((field) => (
                  <div
                    key={field.id}
                    className="flex flex-col"
                  >
                    <BaseLabel
                      htmlFor={field.id}
                      className="text-xs font-medium text-[#3C3C3C] mb-1"
                    >
                      {field.label}
                    </BaseLabel>
                    <BaseInput
                      id={field.id}
                      type="text"
                      disabled
                      className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
                      {...form.register(field.id as keyof IAssignmentForm)}
                    />
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-2 gap-5">
                <div className="flex flex-col">
                  <BaseLabel
                    htmlFor="score"
                    className="text-xs font-medium text-[#3C3C3C] mb-1"
                  >
                    Assignment and Test Score
                  </BaseLabel>
                  <BaseInput
                    id="score"
                    type="text"
                    placeholder="Input assignment and test score"
                    disabled={isQuestions} // <-- DISABLED SAAT MODE QUESTIONS
                    className={cn(
                      'w-full border text-sm rounded-md shadow-none',
                      isQuestions
                        ? 'bg-[#F5F5F5] border-[#DEDEDE] text-[#767676] disabled:opacity-100'
                        : 'border-[#DEDEDE] text-[#3C3C3C]'
                    )}
                    {...form.register('score')}
                  />
                </div>

                <div className="flex flex-col">
                  <BaseLabel
                    htmlFor="retake"
                    className="text-xs font-medium text-[#3C3C3C] mb-1"
                  >
                    With Retake Post Test
                  </BaseLabel>
                  <BaseSelect
                    value={form.watch('retake')}
                    onValueChange={(val) => form.setValue('retake', val)}
                  >
                    <BaseSelectTrigger className="w-full">
                      <BaseSelectValue placeholder="Select" />
                    </BaseSelectTrigger>
                    <BaseSelectContent className="w-full">
                      <BaseSelectItem value="yes">Yes</BaseSelectItem>
                      <BaseSelectItem value="no">No</BaseSelectItem>
                    </BaseSelectContent>
                  </BaseSelect>
                </div>
              </div>
            </div>

            <BaseSeparator className="-my-1" />

            {/* Actions */}
            <div className="flex justify-end gap-3 px-5 pb-4">
              <DialogClose asChild>
                <BaseButton
                  variant="outline"
                  className="h-11 w-32"
                >
                  Cancel
                </BaseButton>
              </DialogClose>
              <BaseButton
                type="submit"
                className="h-11 w-32"
              >
                Submit Score
              </BaseButton>
            </div>
          </form>
        </FormProvider>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default AssignmentAndTestGradingModal;
