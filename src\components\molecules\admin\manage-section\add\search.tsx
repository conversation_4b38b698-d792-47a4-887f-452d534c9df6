import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { Search } from "lucide-react";
import lodash from "lodash";

const QuestionBankSearch = () => {
  const searchByOption = [
    { value: "category_id", label: "Category ID" },
    { value: "category_name", label: "Category Name" },
    { value: "created_by", label: "Created By" },
  ];

  const handleQueryChange = (query: Partial<any>) => {
    console.log(query);
  };

  return (
    <div className="text-[#3C3C3C] font-semibold border rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={""}
          onValueChange={(value) =>
            handleQueryChange({ search_by: value as any })
          }
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {searchByOption.map((it) => (
              <BaseSelectItem key={it.value} value={it.value}>
                {it.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        placeholder="Search"
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={lodash.debounce(
          (e) => handleQueryChange({ page: 1, search: e?.target?.value }),
          800
        )}
      />
      <Search size={16} className="shrink-0 text-[#B1B1B1]" />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default QuestionBankSearch;
