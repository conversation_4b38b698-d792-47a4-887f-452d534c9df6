import React from "react";
import { IGetListMaterialResponse } from "@/interfaces/admin/manage-material/list";
import MediaControls from "./media-controls";
import { useGetLearningMaterialUrlQuery } from "@/services/query/admin/manage-material";

interface VideoPreviewProps {
  material: IGetListMaterialResponse | null;
}

export const VideoPreview = ({ material }: VideoPreviewProps) => {
  const videoRef = React.useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [currentTime, setCurrentTime] = React.useState(0);
  const [duration, setDuration] = React.useState(0);
  const [isMuted, setIsMuted] = React.useState(false);
  const { data: learningMaterialUrl, isPending } =
    useGetLearningMaterialUrlQuery({
      url: material?.link || "",
    });

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  return (
    <div className="rounded-lg h-full flex flex-col border">
      <div className="flex-1 rounded-t-lg overflow-hidden bg-black">
        <video
          ref={videoRef}
          src={learningMaterialUrl?.data?.url || ""}
          className="w-full h-full object-contain"
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
        />
      </div>
      <MediaControls
        isPlaying={isPlaying}
        currentTime={currentTime}
        duration={duration}
        progressPercentage={progressPercentage}
        isMuted={isMuted}
        onTogglePlay={togglePlay}
        onToggleMute={() => setIsMuted(!isMuted)}
        onTimeUpdate={(e) => {
          if (videoRef.current) {
            const rect = e.currentTarget.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const newTime = (clickX / rect.width) * duration;
            videoRef.current.currentTime = newTime;
            setCurrentTime(newTime);
          }
        }}
        onFullscreen={() => videoRef.current?.requestFullscreen()}
      />
    </div>
  );
};

export default VideoPreview;
