import { BaseInput } from '@/components/atoms/input';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/atoms/select';
import { BaseSeparator } from '@/components/atoms/separator';
import { Search } from 'lucide-react';
import React from 'react';

const AssignmentAndTestGradingHeaderSearch = () => {
  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[30%] bg-white px-3">
      <BaseSelect>
        <BaseSelectTrigger className="w-32 border-none px-1">
          <BaseSelectValue placeholder="Search By" />
        </BaseSelectTrigger>
        <BaseSelectContent>
          <BaseSelectItem value="light">Light</BaseSelectItem>
          <BaseSelectItem value="dark">Dark</BaseSelectItem>
        </BaseSelectContent>
      </BaseSelect>
      <BaseSeparator orientation="vertical" />
      <BaseInput className="border-none h-12 focus-visible:border-none focus-visible:ring-0" />
      <Search size={24} />
      <BaseSeparator
        orientation="vertical"
        className="bg-red-500 w-1"
      />
    </div>
  );
};

export default AssignmentAndTestGradingHeaderSearch;
