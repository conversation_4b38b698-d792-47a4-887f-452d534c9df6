"use client";

import { useVirtualizer } from "@tanstack/react-virtual";
import { Check } from "lucide-react";
import { useRef, useEffect } from "react";
import {
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";

interface VirtualizedCommandListProps<T> {
  options: T[];
  selectedValue: string;
  loading: boolean;
  error: string | null;
  loadingSkeleton?: React.ReactNode;
  notFound?: React.ReactNode;
  noResultsMessage?: string;
  label: string;
  getOptionValue: (option: T) => string;
  renderOption: (option: T) => React.ReactNode;
  onSelect: (value: string) => void;
  className?: string;
  itemSize?: number;
}

export function VirtualizedCommandList<T>({
  options,
  selectedValue,
  loading,
  error,
  loadingSkeleton,
  notFound,
  noResultsMessage,
  label,
  getOptionValue,
  renderOption,
  onSelect,
  className,
  itemSize = 36, // Default item height in pixels
}: VirtualizedCommandListProps<T>) {
  const parentRef = useRef<HTMLDivElement>(null);
  const count = options.length;

  const rowVirtualizer = useVirtualizer({
    count,
    getScrollElement: () => parentRef.current,
    estimateSize: () => itemSize,
    overscan: 10,
  });

  // Re-measure when options change
  useEffect(() => {
    rowVirtualizer.measure();
  }, [options, rowVirtualizer]);

  if (error) {
    return <div className="p-4 text-destructive text-center">{error}</div>;
  }

  if (loading && options.length === 0) {
    return loadingSkeleton || <DefaultLoadingSkeleton />;
  }

  if (!loading && !error && options.length === 0) {
    return (
      <CommandEmpty>
        {notFound || noResultsMessage || `No ${label.toLowerCase()} found.`}
      </CommandEmpty>
    );
  }

  const virtualItems = rowVirtualizer.getVirtualItems();
  const totalSize = rowVirtualizer.getTotalSize();

  return (
    <CommandList
      ref={parentRef}
      className={cn("overflow-auto max-h-[300px]", className)}
    >
      <div
        style={{
          height: `${totalSize}px`,
          width: "100%",
          position: "relative",
        }}
      >
        <CommandGroup>
          {virtualItems.map((virtualRow) => {
            const option = options[virtualRow.index];
            if (!option) return null;

            const optionValue = getOptionValue(option);
            return (
              <div
                key={virtualRow.key}
                ref={rowVirtualizer.measureElement}
                data-index={virtualRow.index}
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "100%",
                  transform: `translateY(${virtualRow.start}px)`,
                }}
              >
                <CommandItem
                  value={optionValue}
                  onSelect={onSelect}
                  className="w-full"
                >
                  {renderOption(option)}
                  <Check
                    className={cn(
                      "ml-auto h-3 w-3",
                      selectedValue === optionValue
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                </CommandItem>
              </div>
            );
          })}
        </CommandGroup>
      </div>
    </CommandList>
  );
}

function DefaultLoadingSkeleton() {
  return (
    <CommandGroup>
      {[1, 2, 3].map((i) => (
        <CommandItem key={i} disabled>
          <div className="flex items-center gap-2 w-full">
            <div className="h-6 w-6 rounded-full animate-pulse bg-muted" />
            <div className="flex flex-col flex-1 gap-1">
              <div className="h-4 w-24 animate-pulse bg-muted rounded" />
              <div className="h-3 w-16 animate-pulse bg-muted rounded" />
            </div>
          </div>
        </CommandItem>
      ))}
    </CommandGroup>
  );
}
