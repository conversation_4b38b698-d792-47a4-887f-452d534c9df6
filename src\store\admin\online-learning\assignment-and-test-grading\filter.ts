import { create } from 'zustand';

interface IQuery {
  sectionType?: string;
  dateRange?: { from: Date | null; to: Date | null };
  page?: number;
}

interface IAssignmentAndTestGradingFilter {
  openFilter: boolean;
  setOpenFilter: (open: boolean) => void;
  query: IQuery;
  setQuery: (query: IQuery) => void;
}

export const useAssignmentAndTestGradingFilterStore =
  create<IAssignmentAndTestGradingFilter>()((set) => ({
    openFilter: false,
    setOpenFilter: (open: boolean) => set({ openFilter: open }),
    query: {},
    setQuery: (query: IQuery) => set({ query }),
  }));
