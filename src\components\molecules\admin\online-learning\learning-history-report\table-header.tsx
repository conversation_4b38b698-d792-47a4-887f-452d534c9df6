import React from 'react';
import LearningHistoryReportTableHeaderFilterInput from './filter-input';
import LearningHistoryReportTableHeaderSearch from './search';
import LearningHistoryReportTableHeaderFilter from './filter';

const LearningHistoryReportTableHeader = () => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <LearningHistoryReportTableHeaderSearch />
        <LearningHistoryReportTableHeaderFilter />
      </div>

      <LearningHistoryReportTableHeaderFilterInput />
    </div>
  );
};

export default LearningHistoryReportTableHeader;
