import { create } from 'zustand';

interface IAssignmentAndTestGradingModal {
  openedId: string;
  setOpenedId: (id: string) => void;
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
}

export const useAssignmentAndTestGradingModal =
  create<IAssignmentAndTestGradingModal>()((set) => ({
    openedId: '',
    setOpenedId: (id: string) => set({ openedId: id }),
    openModal: false,
    setOpenModal: (open: boolean) => set({ openModal: open }),
  }));
