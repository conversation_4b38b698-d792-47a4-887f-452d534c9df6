import React from "react";
import { IGetListMaterialResponse } from "@/interfaces/admin/manage-material/list";
import { DUMMY_CONTENT_URLS } from "../preview-modal";
import MediaControls from "./media-controls";
import { useGetLearningMaterialUrlQuery } from "@/services/query/admin/manage-material";

interface AudioPreviewProps {
  material: IGetListMaterialResponse | null;
}

export const AudioPreview = ({ material }: AudioPreviewProps) => {
  const { data: learningMaterialUrl, isPending } =
    useGetLearningMaterialUrlQuery({
      url: material?.link || "",
    });

  const audioRef = React.useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [currentTime, setCurrentTime] = React.useState(0);
  const [duration, setDuration] = React.useState(0);
  const [isMuted, setIsMuted] = React.useState(false);

  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
  // const formatTime = (time: number) => {
  //   const minutes = Math.floor(time / 60);
  //   const seconds = Math.floor(time % 60);
  //   return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  // };

  // Audio wave visualization
  const renderAudioWave = () => {
    return (
      <div className="flex items-end justify-center h-20 gap-1 my-8">
        {[...Array(20)].map((_, i) => {
          const height = 4 + Math.random() * 16;
          return (
            <div
              key={i}
              className={`w-1 bg-black rounded-full transition-all duration-300 ${
                isPlaying ? "animate-pulse" : ""
              }`}
              style={{
                height: `${height}px`,
                animationDelay: `${(i % 10) * 0.1}s`,
              }}
            />
          );
        })}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full rounded-lg border overflow-hidden">
      <audio
        ref={audioRef}
        src={learningMaterialUrl?.data?.url || ""}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        className="hidden"
        preload="auto"
      />

      <div className="flex-1 flex items-center justify-center bg-[#F5F5F5] min-h-[205px]">
        <div className="w-full max-w-2xl">{renderAudioWave()}</div>
      </div>

      <MediaControls
        isPlaying={isPlaying}
        currentTime={currentTime}
        duration={duration}
        progressPercentage={progressPercentage}
        isMuted={isMuted}
        onTogglePlay={togglePlay}
        onToggleMute={() => setIsMuted(!isMuted)}
        onTimeUpdate={(e) => {
          if (audioRef.current) {
            const rect = e.currentTarget.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const newTime = (clickX / rect.width) * duration;
            audioRef.current.currentTime = newTime;
            setCurrentTime(newTime);
          }
        }}
      />
    </div>
  );
};

export default AudioPreview;
