import AssignmentAndTestResultReportTableHeaderFilter from './filter';
import AssignmentAndTestResultReportTableHeaderSearch from './search';
import AssignmentAndTestResultReportFilterInput from './filter-input';

const AssignmentAndTestResultReportTableHeader = () => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <AssignmentAndTestResultReportTableHeaderSearch />
        <AssignmentAndTestResultReportTableHeaderFilter />
      </div>

      <AssignmentAndTestResultReportFilterInput />
    </div>
  );
};

export default AssignmentAndTestResultReportTableHeader;
