import { IGetListMaterialQuery } from "@/interfaces/admin/manage-material/list";
import { create } from "zustand";

interface IManageMaterialQuery {
  materialQuery: IGetListMaterialQuery;
  setMaterialQuery: (query: Partial<IGetListMaterialQuery>) => void;
}

export const useManageMaterialQueryStore = create<IManageMaterialQuery>()(
  (set, get) => ({
    materialQuery: { page: 1, limit: 10, type: "video" },
    setMaterialQuery: (query) =>
      set({ materialQuery: { ...(get().materialQuery as any), ...query } }),
  })
);
