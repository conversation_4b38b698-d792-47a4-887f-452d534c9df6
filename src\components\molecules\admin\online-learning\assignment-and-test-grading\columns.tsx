'use client';

import { ColumnDef } from '@tanstack/react-table';
import { BaseButton } from '@/components/atoms/button';

interface IAssignmentTestGrading {
  userId: string;
  npk: string;
  fullName: string;
  module: string;
  subModule: string;
  section: string;
  sectionType: string;
  passingGrade: number;
  attempt: number;
  sectionAttempt: number;
  issuedDate: string;
}

interface Props {
  onGrade: (id: string) => void;
}

export const getColumnsAssignmentAndTestGrading = ({
  onGrade,
}: Props): ColumnDef<IAssignmentTestGrading>[] => [
  { accessorKey: 'userId', header: 'User ID' },
  { accessorKey: 'npk', header: 'NPK' },
  { accessorKey: 'fullName', header: 'Full Name' },
  { accessorKey: 'module', header: 'Module' },
  { accessorKey: 'subModule', header: 'Sub-Module' },
  { accessorKey: 'section', header: 'Section' },
  { accessorKey: 'sectionType', header: 'Section Type' },
  { accessorKey: 'passingGrade', header: 'Passing Grade' },
  { accessorKey: 'attempt', header: 'Attempt' },
  { accessorKey: 'sectionAttempt', header: 'Section Attempt' },
  { accessorKey: 'issuedDate', header: 'Issued Date' },
  {
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <BaseButton
        variant="outline"
        className="border"
        onClick={() => onGrade(row.original.userId)}
      >
        Grade
      </BaseButton>
    ),
  },
];
