'use client';

import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from '@/components/atoms/dialog';
import { BaseInput } from '@/components/atoms/input';
import { BaseLabel } from '@/components/atoms/label';
import { BaseSeparator } from '@/components/atoms/separator';
import { BaseButton } from '@/components/atoms/button';
import { DialogClose } from '@/components/ui/dialog';
import { useEffect, useMemo } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useLearningHistoryReportDetailModal } from '@/store/admin/online-learning/learning-history-report/modal';
import { DataTable } from '@/components/molecules/global/table';
import type { ColumnDef } from '@tanstack/react-table';

type TLearningRow = {
  module: string;
  attempt: number;
  preTest: number;
  postTestPg: number;
  postTestIsian: number;
  progress: string;
  dateStarted: string;
  dateCompleted: string;
};

type TTrainingRow = {
  type: 'Offline' | 'Online';
  trainingName: string;
  institution: string;
  duration: string;
  dateStarted: string;
  dateCompleted: string;
};

const LEARNING_BASE: TLearningRow = {
  module: 'Module A Contoh',
  attempt: 10,
  preTest: 10,
  postTestPg: 10,
  postTestIsian: 10,
  progress: '100%',
  dateStarted: '01 Aug 2023',
  dateCompleted: '03 Aug 2023',
};

const TRAINING_BASE: TTrainingRow = {
  type: 'Offline',
  trainingName: 'Training 1 Contoh',
  institution: 'AMDI',
  duration: '40 Hours',
  dateStarted: '01 Aug 2023',
  dateCompleted: '03 Aug 2023',
};

const LEARNING_ROWS: TLearningRow[] = Array.from({ length: 10 }).map(() => ({
  ...LEARNING_BASE,
}));
const TRAINING_ROWS: TTrainingRow[] = Array.from({ length: 10 }).map(() => ({
  ...TRAINING_BASE,
}));

const useLearningColumns = (): ColumnDef<TLearningRow, any>[] =>
  useMemo(
    () => [
      { accessorKey: 'module', header: 'Module' },
      { accessorKey: 'attempt', header: 'Attempt' },
      { accessorKey: 'preTest', header: 'Pre-Test' },
      { accessorKey: 'postTestPg', header: 'Post-Test PG' },
      { accessorKey: 'postTestIsian', header: 'Post-Test Isian' },
      { accessorKey: 'progress', header: 'Progress' },
      { accessorKey: 'dateStarted', header: 'Date Started' },
      { accessorKey: 'dateCompleted', header: 'Date Completed' },
    ],
    []
  );

const useTrainingColumns = (): ColumnDef<TTrainingRow, any>[] =>
  useMemo(
    () => [
      { accessorKey: 'type', header: 'Type' },
      { accessorKey: 'trainingName', header: 'Training Name' },
      { accessorKey: 'institution', header: 'Institution' },
      { accessorKey: 'duration', header: 'Duration' },
      { accessorKey: 'dateStarted', header: 'Date Started' },
      { accessorKey: 'dateCompleted', header: 'Date Completed' },
    ],
    []
  );

const LearningHistoryReportDetailModal = () => {
  const { openModal, setOpenModal, setOpenedId } =
    useLearningHistoryReportDetailModal(
      useShallow((state) => ({
        openModal: state.openModal,
        setOpenModal: state.setOpenModal,
        setOpenedId: state.setOpenedId,
      }))
    );

  useEffect(() => {
    if (!openModal) setOpenedId('');
  }, [openModal, setOpenedId]);

  const learningColumns = useLearningColumns();
  const trainingColumns = useTrainingColumns();

  return (
    <BaseDialog
      open={openModal}
      onOpenChange={setOpenModal}
    >
      <BaseDialogContent className="max-w-[980px]! max-h-[92vh] overflow-x-auto scrollbar-hide p-0 gap-0">
        <BaseDialogHeader>
          <BaseDialogTitle className="flex flex-col gap-4 px-6 py-4">
            <p className="text-[#3C3C3C] font-medium">View User Report</p>
          </BaseDialogTitle>
        </BaseDialogHeader>

        <BaseSeparator />

        <div className="flex flex-col gap-6 p-6 text-sm">
          <section className="space-y-3">
            <h3 className="text-[#3C3C3C] text-sm font-semibold">
              User Information
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="flex flex-col">
                <BaseLabel
                  htmlFor="npk"
                  className="text-xs font-medium text-[#3C3C3C] mb-1"
                >
                  NPK
                </BaseLabel>
                <BaseInput
                  id="npk"
                  value="12345"
                  disabled
                  className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
                />
              </div>

              <div className="flex flex-col">
                <BaseLabel
                  htmlFor="fullName"
                  className="text-xs font-medium text-[#3C3C3C] mb-1"
                >
                  Full Name
                </BaseLabel>
                <BaseInput
                  id="fullName"
                  value="Jonathan James Doe"
                  disabled
                  className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
                />
              </div>

              <div className="flex flex-col">
                <BaseLabel
                  htmlFor="jobPosition"
                  className="text-xs font-medium text-[#3C3C3C] mb-1"
                >
                  Job Position
                </BaseLabel>
                <BaseInput
                  id="jobPosition"
                  value="Learning Development An..."
                  disabled
                  className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100"
                />
              </div>
            </div>
          </section>

          <section className="space-y-3">
            <h3 className="text-[#3C3C3C] text-sm font-semibold">
              Section Duration
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="flex flex-col">
                <BaseLabel
                  htmlFor="hours"
                  className="text-xs font-medium text-[#3C3C3C] mb-1"
                >
                  Hours
                </BaseLabel>
                <BaseInput
                  id="hours"
                  type="number"
                  value="04"
                  disabled
                  className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100 appearance-none"
                />
              </div>

              <div className="flex flex-col">
                <BaseLabel
                  htmlFor="minutes"
                  className="text-xs font-medium text-[#3C3C3C] mb-1"
                >
                  Minutes
                </BaseLabel>
                <BaseInput
                  id="minutes"
                  type="number"
                  value="22"
                  disabled
                  className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100 appearance-none"
                />
              </div>

              <div className="flex flex-col">
                <BaseLabel
                  htmlFor="seconds"
                  className="text-xs font-medium text-[#3C3C3C] mb-1"
                >
                  Seconds
                </BaseLabel>
                <BaseInput
                  id="seconds"
                  type="number"
                  value="13"
                  disabled
                  className="w-full bg-[#F5F5F5] border border-[#DEDEDE] text-sm text-[#767676] rounded-md shadow-none disabled:opacity-100 appearance-none"
                />
              </div>
            </div>
          </section>

          <section className="space-y-3">
            <h3 className="text-[#3C3C3C] text-sm font-semibold">
              Learning History Report
            </h3>
            <DataTable<TLearningRow, unknown>
              containerTableClassName="border border-[#DEDEDE]"
              columns={learningColumns}
              data={LEARNING_ROWS}
              pagination={{
                current_page: 1,
                total_page: 10,
                total_data: 8043,
                next: '2',
                prev: null,
              }}
              onPageChange={() => {}}
            />
          </section>

          <section className="space-y-3">
            <h3 className="text-[#3C3C3C] text-sm font-semibold">
              Training History Report
            </h3>
            <DataTable<TTrainingRow, unknown>
              containerTableClassName="border border-[#DEDEDE]"
              columns={trainingColumns}
              data={TRAINING_ROWS}
              pagination={{
                current_page: 1,
                total_page: 10,
                total_data: 8043,
                next: '2',
                prev: null,
              }}
              onPageChange={() => {}}
            />
          </section>
        </div>

        <BaseSeparator />

        <div className="flex items-center justify-end gap-3 px-6 py-4">
          <DialogClose asChild>
            <BaseButton className="h-11 w-32">Close</BaseButton>
          </DialogClose>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default LearningHistoryReportDetailModal;
