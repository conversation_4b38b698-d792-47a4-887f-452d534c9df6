import React from 'react';
import AssignmentAndTestGradingTableHeaderSearch from './search';
import AssignmentAndTestGradingTableHeaderFilter from './filter';
import AssignmentAndTestGradingFilterInput from './filter-input';

const AssignmentAndTestGradingTableHeader = () => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <AssignmentAndTestGradingTableHeaderSearch />
        <AssignmentAndTestGradingTableHeaderFilter />
      </div>

      <AssignmentAndTestGradingFilterInput />
    </div>
  );
};

export default AssignmentAndTestGradingTableHeader;
