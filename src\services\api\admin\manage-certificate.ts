"use server";

import {
  IGetListCertificateQuery,
  IGetListCertificateResponse,
} from "@/interfaces/admin/manage-certificate/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetListCertificates = async (
  params?: IGetListCertificateQuery
) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetListCertificateResponse[]>
    >("/cms/admin/learning/certificate/list", { params });

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};
