import { IGetListCertificateQuery } from "@/interfaces/admin/manage-certificate/list";
import { apiGetListCertificates } from "@/services/api/admin/manage-certificate";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

export const certificateQueryKeys = {
  list: (params?: IGetListCertificateQuery) => ["certificates", params],
  detail: (id: string) => ["certificate", id],
};

export const useGetListCertificatesQuery = (
  params?: IGetListCertificateQuery,
  enabled = true
) => {
  const defaultParams = {
    page: 1,
    limit: 10,
  };

  // Filter out empty values to avoid sending unnecessary parameters
  const filteredParams = Object.fromEntries(
    Object.entries(params ?? {}).filter(([_, value]) => {
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== "" && value !== undefined && value !== null;
    })
  );

  const mergedParams = { ...defaultParams, ...filteredParams };

  return useQuery({
    queryKey: certificateQueryKeys.list(mergedParams),
    queryFn: async () => {
      return await apiGetListCertificates(mergedParams);
    },
    placeholderData: keepPreviousData,
    enabled,
  });
};
