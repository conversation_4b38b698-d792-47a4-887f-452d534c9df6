'use client';

import { ColumnDef } from '@tanstack/react-table';

export interface IAssignmentAndTestResultReport {
  userId: string;
  npk: string;
  fullName: string;
  module: string;
  subModule: string;
  section: string;
  sectionType: string;
  score: number;
}

export const getColumnsAssignmentAndTestResultReport =
  (): ColumnDef<IAssignmentAndTestResultReport>[] => [
    { accessorKey: 'userId', header: 'User ID' },
    { accessorKey: 'npk', header: 'NPK' },
    { accessorKey: 'fullName', header: 'Full Name' },
    { accessorKey: 'module', header: 'Module Name' },
    { accessorKey: 'subModule', header: 'Sub-module' },
    { accessorKey: 'section', header: 'Section' },
    { accessorKey: 'sectionType', header: 'Section Type' },
    { accessorKey: 'score', header: 'Score' },
  ];
