"use client";

import React, { useState } from "react";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseButton } from "@/components/atoms/button";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";
import { OrangeCheckbox } from "@/components/atoms/checkbox/orange-checkbox";
import PillDropdown from "@/components/atoms/dropdown/pill-dropdown";
import QuestionBankTableHeader from "./table-header";
import Pagination from "../common/pagination";

interface QuestionBankModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddQuestions: (questions: any[]) => void;
}

// Dummy question bank data
const questionBankData = [
  {
    id: "12345",
    category: "2 Category",
    level: "4 Levels",
    questionType: "<PERSON><PERSON>han Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12346",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12347",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12348",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12349",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12350",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12351",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12352",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12353",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12354",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
];

const questionTypes = [
  "Video",
  "Audio",
  "PDF",
  "Quiz",
  "Pre-test",
  "Post-test",
];

const QuestionBankModal = ({
  isOpen,
  onClose,
  onAddQuestions,
}: QuestionBankModalProps) => {
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);

  // Pagination state
  const [pageSize, setPageSize] = useState(10);
  const pageSizeOptions = [10, 20, 50, 100];

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedQuestions(questionBankData.map((q) => q.id));
    } else {
      setSelectedQuestions([]);
    }
  };

  const handleSelectQuestion = (questionId: string, checked: boolean) => {
    if (checked) {
      setSelectedQuestions((prev) => [...prev, questionId]);
    } else {
      setSelectedQuestions((prev) => prev.filter((id) => id !== questionId));
    }
  };

  const handleAddQuestions = () => {
    const questionsToAdd = questionBankData.filter((q) =>
      selectedQuestions.includes(q.id)
    );
    onAddQuestions(questionsToAdd);
    onClose();
  };

  const startIndex =
    questionBankData.length === 0 ? 0 : (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, questionBankData.length);
  const currentQuestions = questionBankData.slice(startIndex, endIndex);

  const isAllSelected = selectedQuestions.length === questionBankData.length;

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="sm:max-w-7xl sm:w-[80vw] max-h-[90vh] overflow-auto p-5 flex flex-col">
        <BaseDialogHeader className="flex flex-col items-start justify-between space-y-0">
          <BaseDialogTitle className="text-base font-medium text-comp-content-primary">
            Add Question from Question Bank
          </BaseDialogTitle>
          <hr className=" border-gray-200 -mx-5 w-[calc(100%+40px)]" />
        </BaseDialogHeader>

        <div className="space-y-4">
          <QuestionBankTableHeader />

          {/* Table */}
          <div className="border rounded-lg overflow-auto w-full">
            <div className="max-h-[500px] overflow-auto w-full">
              <BaseTable className="min-w-full">
                <BaseTableHeader>
                  <BaseTableRow className="bg-gray-50">
                    <BaseTableHead className="w-12 sticky top-0 bg-gray-50 z-10">
                      {" "}
                      {/* Make header sticky */}
                      <OrangeCheckbox
                        checked={isAllSelected}
                        ref={React.createRef()}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </BaseTableHead>
                    <BaseTableHead className="w-20 sticky px-3 py-4 top-0 bg-gray-50 z-10">
                      Question ID
                    </BaseTableHead>
                    <BaseTableHead className="w-24 sticky px-3 py-4 top-0 bg-gray-50 z-10">
                      Category
                    </BaseTableHead>
                    <BaseTableHead className="w-20 sticky px-3 py-4 top-0 bg-gray-50 z-10">
                      Level
                    </BaseTableHead>
                    <BaseTableHead className="w-28 sticky px-3 py-4 top-0 bg-gray-50 z-10">
                      Question Type
                    </BaseTableHead>
                    <BaseTableHead className="min-w-[200px] max-w-[400px] w-full bg-gray-50 sticky px-3 py-4 top-0 z-10">
                      Question
                    </BaseTableHead>
                    <BaseTableHead className="w-[200px] sticky px-3 py-4 top-0 bg-gray-50 z-10">
                      Option A
                    </BaseTableHead>
                    <BaseTableHead className="w-[200px] sticky px-3 py-4 top-0 bg-gray-50 z-10">
                      Option B
                    </BaseTableHead>
                    <BaseTableHead className="w-[200px] sticky px-3 py-4 top-0 bg-gray-50 z-10">
                      Option C
                    </BaseTableHead>
                  </BaseTableRow>
                </BaseTableHeader>
                <BaseTableBody>
                  {currentQuestions.map((question) => (
                    <BaseTableRow key={question.id}>
                      <BaseTableCell className="py-4 px-3">
                        <OrangeCheckbox
                          checked={selectedQuestions.includes(question.id)}
                          onCheckedChange={(checked) =>
                            handleSelectQuestion(
                              question.id,
                              checked as boolean
                            )
                          }
                          aria-label={`Select question ${question.id}`}
                        />
                      </BaseTableCell>
                      <BaseTableCell className="font-medium">
                        {question.id}
                      </BaseTableCell>
                      <BaseTableCell className="py-4 px-3">
                        <PillDropdown
                          selected={"2 Sections"}
                          options={[
                            { label: "Sub-Module 1", value: "1" },
                            { label: "Sub-Module 2", value: "2" },
                            { label: "Sub-Module 3", value: "3" },
                          ]}
                          onSelect={(option) => console.log(option)}
                        />
                      </BaseTableCell>
                      <BaseTableCell className="py-4 px-3">
                        <PillDropdown
                          selected={"4 Level"}
                          options={[
                            { label: "Sub-Module 1", value: "1" },
                            { label: "Sub-Module 2", value: "2" },
                            { label: "Sub-Module 3", value: "3" },
                          ]}
                          onSelect={(option) => console.log(option)}
                        />
                      </BaseTableCell>
                      <BaseTableCell className="py-4 px-3">
                        {question.questionType}
                      </BaseTableCell>
                      <BaseTableCell
                        className="min-w-[200px] max-w-[400px] w-full py-4 px-3"
                        title={question.question}
                      >
                        <div className="line-clamp-2 text-wrap">
                          {question.question}
                        </div>
                      </BaseTableCell>
                      <BaseTableCell
                        className="w-[200px] truncate py-4 px-3"
                        title={question.optionA}
                      >
                        {question.optionA}
                      </BaseTableCell>
                      <BaseTableCell
                        className="w-[200px] truncate py-4 px-3"
                        title={question.optionB}
                      >
                        {question.optionB}
                      </BaseTableCell>
                      <BaseTableCell
                        className="w-[200px] truncate py-4 px-3"
                        title={question.optionC}
                      >
                        {question.optionC}
                      </BaseTableCell>
                    </BaseTableRow>
                  ))}
                </BaseTableBody>
              </BaseTable>
            </div>
          </div>

          {/* Pagination */}
          <Pagination
            totalItems={questionBankData.length}
            currentPage={currentPage}
            pageSize={pageSize}
            pageSizeOptions={pageSizeOptions}
            onPageChange={(page) => setCurrentPage(page)}
            onPageSizeChange={(size) => {
              setPageSize(size);
              setCurrentPage(1);
            }}
          />
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <BaseButton variant="outline" onClick={onClose}>
            Cancel
          </BaseButton>
          <BaseButton
            onClick={handleAddQuestions}
            className="bg-orange-500 hover:bg-orange-600"
            disabled={selectedQuestions.length === 0}
          >
            Add Question ({selectedQuestions.length})
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default QuestionBankModal;
