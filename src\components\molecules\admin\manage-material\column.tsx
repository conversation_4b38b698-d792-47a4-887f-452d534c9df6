import { BaseButton } from "@/components/atoms/button";
import { IGetListMaterialResponse } from "@/interfaces/admin/manage-material/list";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { Pencil, Trash2, Download } from "lucide-react";
import IconMaterialVideo from "@/assets/icons/IconMaterialVideo";
import PillDropdown from "./form/pill-dropdown";
import { IMaterialType } from "@/interfaces/admin/manage-material/common";
import IconMaterialAudio from "@/assets/icons/IconMaterialAudio";
import IconMaterialPDF from "@/assets/icons/IconMaterialPDF";

interface Props {
  onEdit: (material: IGetListMaterialResponse) => void;
  onDelete: (material: IGetListMaterialResponse) => void;
  onDownload: (material: IGetListMaterialResponse) => void;
  onPreview?: (material: IGetListMaterialResponse) => void;
  onUpdateField?: (
    id: number,
    field: keyof IGetListMaterialResponse,
    value: any
  ) => void;
  type: IMaterialType;
}

// Helper to format bytes to a readable string
const formatBytes = (bytes?: number) => {
  if (!bytes && bytes !== 0) return "-";
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const value = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${value} ${sizes[i]}`;
};

const icon = {
  video: <IconMaterialVideo />,
  audio: <IconMaterialAudio />,
  document: <IconMaterialPDF />,
  scorm: <IconMaterialPDF />,
};

export const getMaterialColumns = ({
  onEdit,
  onDelete,
  onDownload,
  onPreview,
  onUpdateField,
  type,
}: Props): ColumnDef<IGetListMaterialResponse>[] => {
  return [
    {
      accessorKey: "id",
      header: "ID",
      cell({ row }) {
        return (
          <div className="font-medium text-foreground">{row.original.id}</div>
        );
      },
    },
    {
      accessorKey: "categories",
      header: "Category",
      cell({ row }) {
        const categories = row.original.categories || [];
        const selected = categories[0]?.name ?? "-";
        return (
          <PillDropdown
            selected={selected}
            options={categories.map((category) => ({
              value: category.name,
              id: row.original.id.toString(),
            }))}
            onUpdateField={onUpdateField as any}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "levels",
      header: "Level",
      cell({ row }) {
        const levels = row.original.levels || [];
        const selected = levels[0]?.level ?? "-";
        return (
          <PillDropdown
            selected={selected}
            options={levels.map((level) => ({
              value: level.level,
              id: row.original.id.toString(),
            }))}
            onUpdateField={onUpdateField as any}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "name",
      header: "Document Name",
      cell({ row }) {
        return (
          <div className="flex items-center gap-2 group">
            {icon[type]}
            <button
              onClick={() => onPreview?.(row.original)}
              className="text-xs underline text-blue-600 hover:text-blue-800 cursor-pointer transition-colors"
            >
              {row.original.name}
            </button>
          </div>
        );
      },
    },
    {
      accessorKey: "filesize",
      header: "Size",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {formatBytes(row.original.filesize)}
          </div>
        );
      },
    },
    {
      accessorKey: "associated",
      header: "Associated Sections",
      cell({ row }) {
        const sections = row.original.associated || [];
        return (
          <PillDropdown
            selected={`${sections.length} Sections`}
            options={sections.map((section) => ({
              value: section.name,
              id: row.original.id.toString(),
            }))}
            onUpdateField={onUpdateField as any}
            id={row.original.id.toString()}
          />
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Uploaded At",
      cell({ row }) {
        return (
          <div className="text-sm text-muted-foreground">
            {row.original.created_at
              ? dayjs(row.original.created_at).format("DD MMM YYYY")
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "created_by",
      header: "Uploaded By",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.created_by || "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "last_updated",
      header: "Last Updated",
      cell({ row }) {
        return (
          <div className="text-sm text-muted-foreground">
            {row.original.last_updated
              ? dayjs(row.original.last_updated).format("DD MMM YYYY")
              : "-"}
          </div>
        );
      },
    },
    {
      accessorKey: "updated_by",
      header: "Updated By",
      cell({ row }) {
        return (
          <div className="text-muted-foreground">
            {row.original.updated_by || "-"}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Action",
      cell({ row }) {
        return (
          <div className="flex items-center justify-end gap-1.5">
            <BaseButton
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={() => onDownload(row.original)}
              title="Download"
            >
              <Download className="h-4 w-4 text-gray-500" />
            </BaseButton>
            <BaseButton
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={() => onEdit(row.original)}
              title="Edit"
            >
              <Pencil className="h-4 w-4 text-gray-500" />
            </BaseButton>
            <BaseButton
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 text-destructive hover:bg-destructive/10"
              onClick={() => onDelete(row.original)}
              title="Delete"
            >
              <Trash2 className="h-4 w-4" />
            </BaseButton>
          </div>
        );
      },
    },
  ];
};
