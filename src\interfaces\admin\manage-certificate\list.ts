export interface ICertificate {
  userId: number;
  npk: string;
  fullName: string;
  moduleName: string;
  moduleType: string;
  level: string;
  attempt: number;
  issuedDate: string;
  expiredDate: string;
  status: string;
  active: boolean;
}

export interface IGetListCertificateQuery {
  page?: number;
  limit?: number;
  search?: string;
  search_by?: "npk" | "user_id" | "name" | "module_name" | "level" | "attempt";
  module_type?: string[];
  status?: "active" | "expired";
  start_issue_date?: string; // yyyy-mm-dd
  end_issue_date?: string; // yyyy-mm-dd
  start_expired_date?: string; // yyyy-mm-dd
  end_expired_date?: string; // yyyy-mm-dd
}

export interface IGetListCertificateResponse {
  userId: number;
  npk: string;
  fullName: string;
  moduleName: string;
  moduleType: string;
  level: string;
  attempt: number;
  issuedDate: string;
  expiredDate: string;
  status: string;
  active: boolean;
}

export interface ICertificateFilter {
  moduleType: string[];
  status: "active" | "expired" | undefined;
  issuedDateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  expiredDateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
}

export const MODULE_TYPE_OPTIONS = [
  {
    value: "new_employee_orientation_program",
    label: "New Employee Orientation Program",
  },
  { value: "general_knowledge", label: "General Knowledge" },
  { value: "core_module", label: "Core Module" },
  { value: "welcoming_kit", label: "Welcoming Kit" },
  { value: "working_license", label: "Working License" },
] as const;

export const STATUS_OPTIONS = [
  { value: "active", label: "Active" },
  { value: "expired", label: "Expired" },
] as const;

export const SEARCH_BY_OPTIONS = [
  { value: "npk", label: "NPK" },
  { value: "user_id", label: "User ID" },
  { value: "name", label: "Name" },
  { value: "module_name", label: "Module Name" },
  { value: "level", label: "Level" },
  { value: "attempt", label: "Attempt" },
] as const;
