"use client";

import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { cn } from "@/utils/common";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useState, useEffect } from "react";
import { BaseButton } from "@/components/atoms/button";
import { DateRange } from "react-day-picker";
import {
  MODULE_TYPE_OPTIONS,
  STATUS_OPTIONS,
  ICertificateFilter,
} from "@/interfaces/admin/manage-certificate/list";
import { useManageCertificateFilterStore } from "@/store/admin/manage-certificate/filter";
import { useManageCertificateQueryStore } from "@/store/admin/manage-certificate/query";
import { useShallow } from "zustand/react/shallow";
import { Checkbox } from "@/components/ui/checkbox";

const ManageCertificateFilterInput = () => {
  const { filter, setFilter, resetFilter } = useManageCertificateFilterStore(
    useShallow((state) => ({
      filter: state.filter,
      setFilter: state.setFilter,
      resetFilter: state.resetFilter,
    }))
  );

  const { setCertificateQuery } = useManageCertificateQueryStore(
    useShallow((state) => ({
      setCertificateQuery: state.setCertificateQuery,
    }))
  );

  const handleFilterChange = (key: keyof ICertificateFilter, value: any) => {
    setFilter({ [key]: value });
  };

  const handleDateRangeChange = (
    key: "issuedDateRange" | "expiredDateRange",
    value: DateRange | undefined
  ) => {
    setFilter({
      [key]: {
        from: value?.from,
        to: value?.to,
      },
    });
  };

  const handleModuleTypeChange = (moduleType: string, checked: boolean) => {
    const currentTypes = filter.moduleType || [];
    const newTypes = checked
      ? [...currentTypes, moduleType]
      : currentTypes.filter((type) => type !== moduleType);

    setFilter({ moduleType: newTypes });
  };

  const handleApply = () => {
    // Convert filter to query parameters
    const queryParams: any = {
      page: 1, // Reset to first page when applying filters
    };

    if (filter.moduleType && filter.moduleType.length > 0) {
      queryParams.module_type = filter.moduleType;
    }

    if (filter.status) {
      queryParams.status = filter.status;
    }

    if (filter.issuedDateRange.from) {
      queryParams.start_issue_date = format(
        filter.issuedDateRange.from,
        "yyyy-MM-dd"
      );
    }

    if (filter.issuedDateRange.to) {
      queryParams.end_issue_date = format(
        filter.issuedDateRange.to,
        "yyyy-MM-dd"
      );
    }

    if (filter.expiredDateRange.from) {
      queryParams.start_expired_date = format(
        filter.expiredDateRange.from,
        "yyyy-MM-dd"
      );
    }

    if (filter.expiredDateRange.to) {
      queryParams.end_expired_date = format(
        filter.expiredDateRange.to,
        "yyyy-MM-dd"
      );
    }

    setCertificateQuery(queryParams);
  };

  const handleReset = () => {
    resetFilter();
    setCertificateQuery({
      page: 1,
      module_type: [],
      status: undefined,
      start_issue_date: undefined,
      end_issue_date: undefined,
      start_expired_date: undefined,
      end_expired_date: undefined,
    });
  };

  return (
    <div className="flex flex-col gap-4 w-full bg-white rounded-lg p-3">
      <div className="flex items-center justify-between">
        <span className="font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            variant="outline"
            className="text-red-600 border-red-600 hover:text-red-600 h-9"
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton className="h-9" onClick={handleApply}>
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Module Type Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">
            Module Type (Multiple)
          </BaseLabel>
          <div className="space-y-2 max-h-32 overflow-y-auto border rounded-md p-2">
            {MODULE_TYPE_OPTIONS.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={option.value}
                  checked={filter.moduleType?.includes(option.value) || false}
                  onCheckedChange={(checked) =>
                    handleModuleTypeChange(option.value, checked as boolean)
                  }
                />
                <BaseLabel
                  htmlFor={option.value}
                  className="text-sm font-normal cursor-pointer"
                >
                  {option.label}
                </BaseLabel>
              </div>
            ))}
          </div>
        </div>

        {/* Status Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">Status</BaseLabel>
          <BaseSelect
            value={filter.status || ""}
            onValueChange={(value) => handleFilterChange("status", value)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select status" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {STATUS_OPTIONS.map((option) => (
                <BaseSelectItem key={option.value} value={option.value}>
                  {option.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Date Range Filter */}
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">
            Date Range (Issued Date)
          </BaseLabel>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filter.issuedDateRange.from && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filter.issuedDateRange.from || filter.issuedDateRange.to ? (
                    <div className="flex items-center gap-2">
                      {filter.issuedDateRange.from &&
                        format(filter.issuedDateRange.from, "dd MMM yyyy")}
                      <span>-</span>
                      {filter.issuedDateRange.to &&
                        format(filter.issuedDateRange.to, "dd MMM yyyy")}
                    </div>
                  ) : (
                    <span>Issued Date</span>
                  )}
                </BaseButton>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  captionLayout="dropdown"
                  mode="range"
                  selected={filter.issuedDateRange}
                  onSelect={(date) =>
                    handleDateRangeChange("issuedDateRange", date)
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <BaseLabel className="text-sm font-medium">
            Date Range (Expired Date)
          </BaseLabel>
          <div className="flex gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <BaseButton
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !filter.expiredDateRange.from && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filter.expiredDateRange.from ? (
                    <div className="flex items-center gap-2">
                      {filter.expiredDateRange.from &&
                        format(filter.expiredDateRange.from, "dd MMM yyyy")}
                      <span>-</span>
                      {filter.expiredDateRange.to &&
                        format(filter.expiredDateRange.to, "dd MMM yyyy")}
                    </div>
                  ) : (
                    <span>Expired Date</span>
                  )}
                </BaseButton>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="range"
                  selected={filter.expiredDateRange}
                  onSelect={(date) =>
                    handleDateRangeChange("expiredDateRange", date)
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManageCertificateFilterInput;
