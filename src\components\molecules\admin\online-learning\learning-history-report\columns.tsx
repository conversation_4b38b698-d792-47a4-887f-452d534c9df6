'use client';

import { ColumnDef } from '@tanstack/react-table';
import { BaseButton } from '@/components/atoms/button';
import { Eye } from 'lucide-react';

export interface ILearningHistoryReport {
  npk: string;
  inProgressModule: number;
  completedModule: number;
  preTest: number;
  postTestPg: number;
  postTestIsian: number;
  studyTime: string;
  trainingHistory: number;
}

interface Props {
  onDetail: (id: string) => void;
}

export const getColumnsLearningHistoryReport = ({
  onDetail,
}: Props): ColumnDef<ILearningHistoryReport>[] => [
  { accessorKey: 'npk', header: 'NPK' },
  { accessorKey: 'inProgressModule', header: 'In-Progress Module' },
  { accessorKey: 'completedModule', header: 'Completed Module' },
  { accessorKey: 'preTest', header: 'Pre-Test' },
  { accessorKey: 'postTestPg', header: 'Post-Test PG' },
  { accessorKey: 'postTestIsian', header: 'Post-Test Isian' },
  { accessorKey: 'studyTime', header: 'Study Time' },
  { accessorKey: 'trainingHistory', header: 'Training History' },
  {
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <BaseButton
        variant="ghost"
        size="icon"
        onClick={() => onDetail(row.original.npk)}
        aria-label="View Detail"
      >
        <Eye size={18} />
      </BaseButton>
    ),
  },
];
