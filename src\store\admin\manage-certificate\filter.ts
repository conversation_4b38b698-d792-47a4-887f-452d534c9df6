import { ICertificateFilter } from "@/interfaces/admin/manage-certificate/list";
import { create } from "zustand";

interface IManageCertificateFilter {
  openFilter: boolean;
  setOpenFilter: (open: boolean) => void;
  filter: ICertificateFilter;
  setFilter: (filter: Partial<ICertificateFilter>) => void;
  resetFilter: () => void;
  applyFilter: () => void;
}

const initialFilter: ICertificateFilter = {
  moduleType: [],
  status: undefined,
  issuedDateRange: {
    from: undefined,
    to: undefined,
  },
  expiredDateRange: {
    from: undefined,
    to: undefined,
  },
};

export const useManageCertificateFilterStore = create<IManageCertificateFilter>()(
  (set, get) => ({
    openFilter: false,
    setOpenFilter: (open: boolean) => set({ openFilter: open }),
    filter: initialFilter,
    setFilter: (filter) =>
      set({
        filter: { ...get().filter, ...filter },
      }),
    resetFilter: () => set({ filter: initialFilter }),
    applyFilter: () => {
      // This will be called when the user clicks apply
      // The actual application logic will be in the component
      console.log("Applying filter:", get().filter);
    },
  })
);
