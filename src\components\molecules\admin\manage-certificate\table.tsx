"use client";

import React from "react";
import { DataTable } from "../../global/table";
import { ICertificate } from "@/interfaces/admin/manage-certificate/list";
import { getCertificateColumns } from "./column";
import ManageCertificateTableHeader from "./table-header";
import { useRouter } from "next/navigation";
import ConfirmationModal from "../../modal/confirmation-modal";
import { useGetListCertificatesQuery } from "@/services/query/admin/manage-certificate";
import { useManageCertificateQueryStore } from "@/store/admin/manage-certificate/query";
import { useShallow } from "zustand/react/shallow";

type IModalData = {
  isOpen: boolean;
  data: ICertificate | null;
};

const ManageCertificateTable = () => {
  const router = useRouter();
  const [openModal, setOpenModal] = React.useState<IModalData>({
    isOpen: false,
    data: null,
  });

  const { certificateQuery, setCertificateQuery } =
    useManageCertificateQueryStore(
      useShallow((state) => ({
        certificateQuery: state.certificateQuery,
        setCertificateQuery: state.setCertificateQuery,
      }))
    );

  const { data, isLoading, error } =
    useGetListCertificatesQuery(certificateQuery);

  const columns = React.useMemo(
    () =>
      getCertificateColumns({
        onView: (certificate) => {
          router.push(
            `/admin/manage-certificate/certificate/${certificate.npk}`
          );
        },
        onToggleActive: (certificate) => {
          setOpenModal({ isOpen: true, data: certificate });
        },
      }),
    []
  );

  const handlePageChange = (page: number) => {
    setCertificateQuery({ page });
  };

  const handleToggleActive = () => {
    // TODO: Implement toggle active API call
    setOpenModal({ isOpen: false, data: null });
  };

  if (error) {
    return (
      <div className="flex flex-col gap-4 h-full">
        <ManageCertificateTableHeader />
        <div className="flex items-center justify-center h-64">
          <p className="text-red-500">
            Error loading certificates: {error.message}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 h-full">
        <ManageCertificateTableHeader />
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">Loading certificates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 h-full">
      <ConfirmationModal
        isOpen={openModal.isOpen}
        onOpenChange={(open) => setOpenModal({ isOpen: open, data: null })}
        onConfirm={handleToggleActive}
        title={`${
          openModal.data?.active ? "Nonaktifkan" : "Aktifkan"
        } Certificate?`}
        description={`Anda yakin ingin ${
          openModal.data?.active ? "nonaktifkan" : "aktifkan"
        } certificate ini?`}
      />
      <ManageCertificateTableHeader />
      <DataTable
        columns={columns}
        data={data?.data || []}
        pagination={
          data?.pagination || {
            current_page: 1,
            total_page: 1,
            total_data: 0,
            next: null,
            prev: null,
          }
        }
        onPageChange={handlePageChange}
      />
    </div>
  );
};

export default ManageCertificateTable;
