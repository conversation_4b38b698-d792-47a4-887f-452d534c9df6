'use client';

import { BaseButton } from '@/components/atoms/button';
import { DownloadCloud, Settings2 } from 'lucide-react';
import { useShallow } from 'zustand/react/shallow';
import { cn } from '@/lib/utils';
import { useAssignmentAndTestResultReportFilterStore } from '@/store/admin/online-learning/assignment-and-test-result-report/filter';

const AssignmentAndTestResultReportTableHeaderFilter = () => {
  const { openFilter, setOpenFilter } = useAssignmentAndTestResultReportFilterStore(
    useShallow((state) => ({
      openFilter: state.openFilter,
      setOpenFilter: state.setOpenFilter,
    }))
  );

  return (
    <div className="flex justify-end gap-3">
      <BaseButton
        variant={'outline'}
        size={'icon'}
        className="size-12"
      >
        <DownloadCloud />
      </BaseButton>
      <BaseButton
        variant={'outline'}
        className={cn('h-12 px-8', openFilter && 'bg-gray-200')}
        onClick={() => setOpenFilter(!openFilter)}
        ref={null}
      >
        <div className="flex items-center gap-2">
          <Settings2 />
          Filter
        </div>
      </BaseButton>
    </div>
  );
};

export default AssignmentAndTestResultReportTableHeaderFilter;
