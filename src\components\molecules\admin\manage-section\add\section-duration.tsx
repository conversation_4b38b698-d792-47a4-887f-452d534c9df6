"use client";

import React from "react";
import { Control, FieldErrors } from "react-hook-form";
import { AddSectionFormData } from "@/components/organisms/admin/manage-section/add-new-section";
import { BaseInput } from "@/components/atoms/input";
import { Controller } from "react-hook-form";

interface SectionDurationProps {
  control: Control<AddSectionFormData>;
  errors: FieldErrors<AddSectionFormData>;
}

const SectionDuration = ({ control, errors }: SectionDurationProps) => {
  return (
    <div className="bg-white">
      <h2 className="text-sm font-medium text-comp-content-primary mb-3">
        Section Duration
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        {/* Hours */}
        <div>
          <label className="block text-sm font-medium text-comp-content-primary mb-2">
            Hours
          </label>
          <Controller
            name="hours"
            control={control}
            render={({ field }) => (
              <BaseInput
                {...field}
                type="number"
                min="0"
                placeholder="Input hours"
                onChange={(e) => field.onChange(Number(e.target.value))}
                className={errors.hours ? "border-red-500" : ""}
              />
            )}
          />
          {errors.hours && (
            <p className="text-red-500 text-sm mt-1">{errors.hours.message}</p>
          )}
        </div>

        {/* Minutes */}
        <div>
          <label className="block text-sm font-medium text-comp-content-primary mb-2">
            Minutes
          </label>
          <Controller
            name="minutes"
            control={control}
            render={({ field }) => (
              <BaseInput
                {...field}
                type="number"
                min="0"
                max="59"
                placeholder="Input minutes"
                onChange={(e) => field.onChange(Number(e.target.value))}
                className={errors.minutes ? "border-red-500" : ""}
              />
            )}
          />
          {errors.minutes && (
            <p className="text-red-500 text-sm mt-1">
              {errors.minutes.message}
            </p>
          )}
        </div>

        {/* Seconds */}
        <div>
          <label className="block text-sm font-medium text-comp-content-primary mb-2">
            Seconds
          </label>
          <Controller
            name="seconds"
            control={control}
            render={({ field }) => (
              <BaseInput
                {...field}
                type="number"
                min="0"
                max="59"
                placeholder="Input seconds"
                onChange={(e) => field.onChange(Number(e.target.value))}
                className={errors.seconds ? "border-red-500" : ""}
              />
            )}
          />
          {errors.seconds && (
            <p className="text-red-500 text-sm mt-1">
              {errors.seconds.message}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default SectionDuration;
