'use client';

import { useMemo, useState } from 'react';
import { DataTable } from '@/components/molecules/global/table';
import { getColumnsAssignmentAndTestGrading } from './columns';
import AssignmentTestAndGradeModal from './grade-modal';
import AssignmentAndTestGradingTableHeader from './table-header';
import { useAssignmentAndTestGradingModal } from '@/store/admin/online-learning/assignment-and-test-grading/modal';

const baseItem = {
  userId: '12345',
  npk: '12345',
  fullName: 'Nama Learner',
  module: 'Module NEOP Contoh A',
  subModule: 'Sub Module Post Test',
  section: 'Section A Post Test',
  sectionType: 'Post-test <PERSON><PERSON><PERSON>anda',
  passingGrade: 70,
  attempt: 1,
  sectionAttempt: 1,
  issuedDate: '31 Aug 20230 11.59',
};

const dummyData = Array.from({ length: 20 }).map((_, i) => ({
  ...baseItem,
  userId: String(10000 + i),
  npk: String(10000 + i),
  fullName: `<PERSON><PERSON> ${i + 1}`,
  section: `Section A Post Test #${i + 1}`,
}));

const PAGE_SIZE = 10;

const AssignmentAndTestGradingTable = () => {
  const { setOpenModal, setOpenedId } = useAssignmentAndTestGradingModal();
  const [page, setPage] = useState(1);

  const columns = useMemo(
    () =>
      getColumnsAssignmentAndTestGrading({
        onGrade: (id) => {
          setOpenedId(id);
          setOpenModal(true);
        },
      }),
    [setOpenModal, setOpenedId]
  );

  const pagedData = useMemo(() => {
    const start = (page - 1) * PAGE_SIZE;
    return dummyData.slice(start, start + PAGE_SIZE);
  }, [page]);

  const totalPage = Math.ceil(dummyData.length / PAGE_SIZE);

  return (
    <div className="flex flex-col gap-4">
      <AssignmentAndTestGradingTableHeader />
      <DataTable
        containerClassName="max-h-[calc(100vh-280px)] overflow-y-auto"
        columns={columns}
        data={pagedData}
        pagination={{
          current_page: page,
          total_page: totalPage,
          total_data: dummyData.length,
          next: page < totalPage ? String(page + 1) : null,
          prev: page > 1 ? String(page - 1) : null,
        }}
        onPageChange={setPage}
      />

      <AssignmentTestAndGradeModal />
    </div>
  );
};

export default AssignmentAndTestGradingTable;
