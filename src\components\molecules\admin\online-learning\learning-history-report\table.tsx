'use client';

import { useMemo, useState } from 'react';
import { DataTable } from '@/components/molecules/global/table';
import { getColumnsLearningHistoryReport } from './columns';
import LearningHistoryReportTableHeader from './table-header';
import LearningHistoryReportDetailModal from './detail-modal';
import { useLearningHistoryReportDetailModal } from '@/store/admin/online-learning/learning-history-report/modal';

const baseItem = {
  npk: '12345',
  inProgressModule: 10,
  completedModule: 10,
  preTest: 10,
  postTestPg: 10,
  postTestIsian: 10,
  studyTime: '12 hrs 02 mnt 00 sec',
  trainingHistory: 2,
};

const dummyData = Array.from({ length: 20 }).map((_, i) => ({
  ...baseItem,
  npk: String(10000 + i),
}));

const PAGE_SIZE = 10;

const LearningHistoryReportTable = () => {
  const { setOpenModal, setOpenedId } = useLearningHistoryReportDetailModal();
  const [page, setPage] = useState(1);

  const columns = useMemo(
    () =>
      getColumnsLearningHistoryReport({
        onDetail: (npk) => {
          setOpenedId(npk);
          setOpenModal(true);
        },
      }),
    [setOpenModal, setOpenedId]
  );

  const pagedData = useMemo(() => {
    const start = (page - 1) * PAGE_SIZE;
    return dummyData.slice(start, start + PAGE_SIZE);
  }, [page]);

  const totalPage = Math.ceil(dummyData.length / PAGE_SIZE);

  return (
    <div className="flex flex-col gap-4">
      <LearningHistoryReportTableHeader />
      <DataTable
        containerClassName="max-h-[calc(100vh-280px)] overflow-y-auto"
        columns={columns}
        data={pagedData}
        pagination={{
          current_page: page,
          total_page: totalPage,
          total_data: dummyData.length,
          next: page < totalPage ? String(page + 1) : null,
          prev: page > 1 ? String(page - 1) : null,
        }}
        onPageChange={setPage}
      />

      <LearningHistoryReportDetailModal />
    </div>
  );
};

export default LearningHistoryReportTable;
