import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import {
  IGetListCategoryQuery,
  IGetListSubCategoryQuery,
} from "@/interfaces/admin/manage-category/list";
import { useManageCategoryQueryStore } from "@/store/admin/manage-category/query";
import { useManageCategoryTabStore } from "@/store/admin/manage-category/tab";
import { Search } from "lucide-react";
import React from "react";
import { useShallow } from "zustand/react/shallow";
import lodash from "lodash";
import { useManageMaterialQueryStore } from "@/store/admin/manage-material/query";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import { IGetListMaterialQuery } from "@/interfaces/admin/manage-material/list";

const ManageMaterialTableHeaderSearch = () => {
  const activeTab = useManageMaterialTabStore((state) => state.activeTab);
  const { materialQuery, setMaterialQuery } = useManageMaterialQueryStore(
    useShallow((state) => ({
      materialQuery: state.materialQuery,
      setMaterialQuery: state.setMaterialQuery,
    }))
  );

  const searchByOption = React.useMemo(() => {
    return [
      { value: "name", label: "Name" },
      { value: "associated", label: "Section Name" },
      { value: "created_by", label: "Created By" },
      { value: "updated_by", label: "Updated By" },
    ];
  }, [activeTab]);

  const handleQueryChange = (query: Partial<IGetListMaterialQuery>) => {
    setMaterialQuery(query);
  };

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[40%] bg-white px-3">
      <div>
        <BaseSelect
          value={materialQuery.search_by}
          onValueChange={(value) =>
            handleQueryChange({ search_by: value as any })
          }
        >
          <BaseSelectTrigger className="w-44 border-none px-1">
            <BaseSelectValue placeholder="Search By" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {searchByOption.map((it) => (
              <BaseSelectItem key={it.value} value={it.value}>
                {it.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={lodash.debounce(
          (e) => handleQueryChange({ page: 1, search: e?.target?.value }),
          800
        )}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default ManageMaterialTableHeaderSearch;
