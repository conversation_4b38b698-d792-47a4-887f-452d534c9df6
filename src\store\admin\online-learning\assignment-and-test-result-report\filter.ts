import { create } from 'zustand';

interface IQuery {
  sectionType?: string;
  status?: string;
  dateRange?: { from: Date | null; to: Date | null };
  page?: number;
}

interface IAssignmentAndTestResultReportFilter {
  openFilter: boolean;
  setOpenFilter: (open: boolean) => void;
  query: IQuery;
  setQuery: (query: IQuery) => void;
}

export const useAssignmentAndTestResultReportFilterStore =
  create<IAssignmentAndTestResultReportFilter>()((set) => ({
    openFilter: false,
    setOpenFilter: (open: boolean) => set({ openFilter: open }),
    query: {},
    setQuery: (query: IQuery) => set({ query }),
  }));
