export interface IMaterial {
  id: string;
  document_id: string;
  category: string;
  level: string;
  document_name: string;
  size: string;
  associated_sections: string[];
  uploaded_at: string;
  uploaded_by: string;
  updated_at: string;
  updated_by: string;
}

export interface IGetListMaterialQuery {
  page: number;
  limit?: number;
  search?: string;
  search_by?: "name" | "associated" | "created_by" | "updated_by";
  category_id?: string;
  level_id?: string;
  type: "video" | "document" | "audio";
  feature?: "OnlineLearning";
}

export type IGetListMaterialResponse = {
  id: number;
  type: string;
  name: string;
  file_format: string;
  levels: ILevel[];
  categories: IAssociated[];
  associated: IAssociated[];
  link: string;
  filesize: number;
  feature: string;
  created_at: Date;
  created_by: string;
  updated_by: string;
  last_updated: Date;
};

export type IAssociated = {
  id: number;
  name: string;
};

export type ILevel = {
  id: number;
  level: string;
};
