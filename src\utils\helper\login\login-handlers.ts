import {
  Step1Values,
  Step2Values,
  Step3Values,
  Step4Values,
  Step5Values,
  useLoginFlow,
} from "@/hooks/useLoginFlow";
import { jwtDecode } from "jwt-decode";
import {
  ICheckMfaRequirementRequest,
  ISendOtpRequest,
  IVerifyMfaRequest,
  IVerifyMfaSetupRequest,
  IVerifyOtpRequest,
} from "@/interfaces/user/mfa";
import { IUpdatePasswordRequest } from "@/interfaces/user/login";
import {
  clearNeedUpdatePassword,
  setNeedUpdatePassword,
  setSession,
} from "@/services/session/session";

export const createLoginHandlers = (
  loginFlow: ReturnType<typeof useLoginFlow>
) => {
  const {
    verifyUser,
    loginUser,
    sendOtp,
    verifyOtp,
    updatePassword,
    checkMfaRequirement,
    enrollMfa,
    verifyMfaSetup,
    verifyMfa,
    setSelectedUser,
    setStep,
    otpDialog,
    setOtpDialog,
    setOtpMethod,
    setResendTimer,
    setMfaData,
    setIsMfaOtp,
    setInstructionDialogOpen,
    selectedUser,
    otpMethod,
    isMfaOtp,
    mfaData,
    resetForms,
    router,
    notifyHotError,
    notifyHotSuccess,
    encryptAES,
    getVisitorId,
    checkPrivateMode,
    step1Form,
    step2Form,
    session,
  } = loginFlow;

  const handleStep1Submit = async (data: Step1Values) => {
    try {
      const found = await verifyUser({ username: data.email });
      if (found.status === false) {
        step1Form.setError("email", {
          type: "manual",
          message: "User not found",
        });
      } else {
        setSelectedUser({
          user_id: found.data.id,
          name: found.data.name,
          email: data.email,
          phone: "",
          password: "",
          status: found.data.is_active ? "active" : "inactive",
          is_new_user: found.data.is_new_user,
          key: "",
        });
        setStep(2);
      }
    } catch {
      notifyHotError("Error", "Something went wrong");
      return;
    }

    resetForms();
  };

  const handleStep2Submit = async (data: Step2Values) => {
    try {
      if (!selectedUser?.email) return;
      const encryptedPassword = encryptAES(data.password);
      const res = await loginUser({
        username: selectedUser.email,
        password: encryptedPassword,
      });
      if (res.status === false) {
        step2Form.setError("password", {
          type: "manual",
          message: "The password you entered is incorrect. Please try again.",
        });
      } else {
        setSelectedUser({ ...selectedUser, phone: res.data.phone });
        const isCandidate = res.data.role_id === 6;
        if (isCandidate) {
          if (res.data.is_new_user) {
            setOtpDialog({ open: true, type: "login" });
          } else {
            // set token
            if (res.data.token) {
              await setSession(res.data.token);
              notifyHotSuccess("Welcome", "Login berhasil!");
              router.push("/homepage");
            }
          }
        } else {
          const riskLevel = res.data.role_id === 1 ? "MEDIUM" : "HIGH";
          const visitorId = await getVisitorId();
          const { isPrivate } = await checkPrivateMode();
          const payload: ICheckMfaRequirementRequest = {
            isPrivate,
            riskLevel,
            userIdentifier: `mfa-lemon-${selectedUser?.email ?? ""}`,
            visitorId,
            forceMfa: false,
          };
          try {
            const requirement = await checkMfaRequirement(payload);
            if (requirement.data.isEnrolled && requirement.data.mfaRequired) {
              setIsMfaOtp(true);
              setStep(3);
            } else if (
              requirement.data.isEnrolled &&
              !requirement.data.mfaRequired
            ) {
              if (requirement.data.token) {
                await setSession(requirement.data.token);
                notifyHotSuccess("Welcome", "Login berhasil!");
                router.push("/homepage");
              }
            } else if (!requirement.data.isEnrolled) {
              try {
                const res = await enrollMfa({
                  userIdentifier: `mfa-lemon-${selectedUser?.email ?? ""}`,
                  userName: selectedUser?.name ?? "",
                });
                setMfaData(res.data);
                setStep(4);
              } catch {
                notifyHotError("Error", "Something went wrong");
                return;
              }
            }
          } catch (error) {
            notifyHotError(
              "Error",
              (error as Error).message ?? "Something went wrong"
            );
            return;
          }
        }
      }
    } catch {
      step2Form.setError("password", {
        type: "manual",
        message: "The password you entered is incorrect. Please try again.",
      });
      return;
    }
  };

  const handleStep3Submit = async (
    data: Step3Values,
    otpType: "login" | "forgot_password"
  ) => {
    try {
      if (isMfaOtp) {
        const payload: IVerifyMfaRequest = {
          userIdentifier: `mfa-lemon-${selectedUser?.email ?? ""}`,
          token: data.otp,
        };

        const res = await verifyMfa(payload);
        if (res.status === true) {
          if (res.data.token) {
            await setSession(res.data.token);
            notifyHotSuccess("Welcome", "Login berhasil!");
            router.push("/homepage");
          }
        }
        return;
      }
      const payload: IVerifyOtpRequest = {
        otp: data.otp,
        otp_type: otpType,
        key: selectedUser?.key ?? "",
      };
      const res = await verifyOtp(payload);

      if (res.status === true) {
        if (otpType === "forgot_password") {
          await setNeedUpdatePassword();
          setStep(5);
          return;
        }
        const isNewUser = res.data.is_new_user;
        if (isNewUser) {
          await setNeedUpdatePassword();
          setStep(5);
          return;
        } else {
          if (res.data.token) {
            await setSession(res.data.token);
            notifyHotSuccess("Welcome", "Login berhasil!");
            router.push("/homepage");
            return;
          }
        }
      }
    } catch {
      notifyHotError(
        "Invalid OTP code",
        "You have entered an invalid OTP. Please try again!"
      );
    }
  };

  const handleStep4Submit = async (data: Step4Values) => {
    try {
      const payload: IVerifyMfaSetupRequest = {
        userIdentifier: `mfa-lemon-${selectedUser?.email ?? ""}`,
        token: data.otpMfa,
      };
      const res = await verifyMfaSetup(payload);
      if (res.status === true) {
        if (selectedUser?.is_new_user) {
          await setNeedUpdatePassword();
          setStep(5);
          return;
        } else {
          if (res.data.token) {
            await setSession(res.data.token);
            notifyHotSuccess("Welcome", "Login berhasil!");
            router.push("/homepage");
            return;
          }
        }
      }
    } catch {
      notifyHotError(
        "Invalid OTP code",
        "You have entered an invalid OTP. Please try again!"
      );
    }
  };

  const handleStep5Submit = async (data: Step5Values) => {
    const decoded = jwtDecode(session?.accessToken ?? "") as {
      user_id: number;
    };
    try {
      const updatePasswordPayload: IUpdatePasswordRequest = {
        new_password: encryptAES(data.newPassword),
        confirm_new_password: encryptAES(data.confirmPassword),
      };
      const res = await updatePassword({
        body: updatePasswordPayload,
        userId: selectedUser?.user_id ?? decoded.user_id ?? 0,
      });
      if (res.status === true) {
        await clearNeedUpdatePassword();
        notifyHotSuccess(
          "Success",
          "Password kamu sudah diset. Mengarahkan ke homepage…"
        );
        router.push("/homepage");
      }
    } catch {
      notifyHotError("Error", "Something went wrong");
      return;
    }
  };

  const handleResendOtp = async () => {
    if (!selectedUser?.user_id) return;

    const payload: ISendOtpRequest = {
      user_id: selectedUser?.user_id,
      otp_type: "login",
      send_to: selectedUser?.[otpMethod || "phone"] ?? "",
      provider: otpMethod === "email" ? "email" : "phone",
    };
    const resSendOtp = await sendOtp(payload);
    if (!resSendOtp.status) return;
    setSelectedUser({
      ...selectedUser,
      key: resSendOtp.data.key,
    });
    notifyHotSuccess("Success", "Your OTP has been sent");
    setResendTimer(90);
  };

  const handleSelectOtpMethod = async (method: "email" | "phone") => {
    if (!selectedUser?.user_id) return;

    const payload: ISendOtpRequest = {
      user_id: selectedUser?.user_id,
      otp_type: otpDialog.type,
      send_to: selectedUser?.[method] ?? "",
      provider: method === "email" ? "email" : "phone",
    };
    const resSendOtp = await sendOtp(payload);
    if (resSendOtp.status === true) {
      setSelectedUser({ ...selectedUser, key: resSendOtp.data.key });
      setOtpDialog({ open: false, type: otpDialog.type });
      setOtpMethod(method);
      setResendTimer(90);
      setStep(3);
    } else {
      notifyHotError("Error", resSendOtp.message);
    }
  };

  const handleCopySecret = async () => {
    try {
      await navigator.clipboard.writeText(mfaData?.setupKey ?? "");
      notifyHotSuccess("Copied", "Secret key copied to clipboard");
    } catch {
      notifyHotError("Copy failed", "Could not copy the secret key");
    }
  };

  const handleOpenInstructions = () => {
    setInstructionDialogOpen(true);
  };

  const handleBackToStep1 = () => {
    setStep(1);
  };

  const handleForgotPassword = async () => {
    if (!selectedUser?.user_id) return;
    const payload: ISendOtpRequest = {
      user_id: selectedUser?.user_id,
      otp_type: "forgot_password",
      send_to: selectedUser?.[otpMethod || "phone"] ?? "",
      provider: otpMethod === "email" ? "email" : "phone",
    };
    const resSendOtp = await sendOtp(payload);
    if (!resSendOtp.status) return;
    setSelectedUser({
      ...selectedUser,
      key: resSendOtp.data.key,
    });
    notifyHotSuccess("Success", "Your OTP has been sent");
    setResendTimer(90);
    setStep(3);
  };

  const handleOpenOtpMethodDialog = (type: "login" | "forgot_password") => {
    setOtpDialog({ open: true, type });
  };

  return {
    handleStep1Submit,
    handleStep2Submit,
    handleStep3Submit,
    handleStep4Submit,
    handleStep5Submit,
    handleResendOtp,
    handleSelectOtpMethod,
    handleCopySecret,
    handleOpenInstructions,
    handleBackToStep1,
    handleForgotPassword,
    handleOpenOtpMethodDialog,
  };
};
