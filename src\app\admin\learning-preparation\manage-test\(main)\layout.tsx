import ManageTestTitle from "@/components/molecules/admin/manage-test/common/title";
import React from "react";
import { Metadata } from "next";

interface Props {
  children: React.ReactNode;
}

export const metadata: Metadata = {
  title: "Manage Test | Lemon App",
  description: "Administrative dashboard for managing the Lemon application",
};

const ManageTestLayout = ({ children }: Readonly<Props>) => {
  return (
    <div className="flex flex-col gap-7">
      <ManageTestTitle />
      {children}
    </div>
  );
};

export default ManageTestLayout;
