import { keepPreviousData, useQuery } from "@tanstack/react-query";
import {
  IGetListFaqParams,
  IGetListTagParams,
} from "@/interfaces/admin/manage-faq/list";
import {
  apiDetailFaq,
  apiGetListFaq,
  apiGetListTag,
} from "@/services/api/admin/manage-faq";

export const faqQueryKeys = {
  list: (params?: IGetListFaqParams) => ["faq", "list", params],
  detail: (id: string) => ["faq", "detail", id],
};

export const tagQueryKeys = {
  list: (params: IGetListTagParams) => ["tag", "list", params],
};

export const useGetListFaqQuery = (params?: IGetListFaqParams) => {
  const defaultParams = {
    page: 1,
    limit: 10,
  };
  // if params key empty dont send it
  const filteredParams = Object.fromEntries(
    Object.entries(params ?? {}).filter(([_, value]) => value !== "")
  );
  const mergedParams = { ...defaultParams, ...filteredParams };
  console.log("mergedParams", mergedParams);

  return useQuery({
    queryKey: faqQueryKeys.list(mergedParams),
    queryFn: async () => {
      return await apiGetListFaq(mergedParams);
    },
    placeholderData: keepPreviousData,
  });
};

export const useGetListTagQuery = (params: IGetListTagParams) => {
  return useQuery({
    queryKey: tagQueryKeys.list(params),
    queryFn: async () => {
      return await apiGetListTag(params);
    },
    placeholderData: keepPreviousData,
  });
};

export const useDetailFaqQuery = (id: string) => {
  return useQuery({
    queryKey: faqQueryKeys.detail(id),
    queryFn: async () => {
      return await apiDetailFaq(id);
    },
    enabled: !!id,
  });
};
