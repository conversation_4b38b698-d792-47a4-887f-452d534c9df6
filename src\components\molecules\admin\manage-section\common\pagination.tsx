"use client";

import React from "react";
import { BaseButton } from "@/components/atoms/button";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseInput } from "@/components/atoms/input";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaginationProps {
  totalItems: number;
  currentPage: number;
  pageSize: number;
  pageSizeOptions?: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  totalItems,
  currentPage,
  pageSize,
  pageSizeOptions = [10, 20, 50, 100],
  onPageChange,
  onPageSizeChange,
}) => {
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));
  const startIndex = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalItems);
  const [goto, setGoto] = React.useState<string>("");

  const renderPageButtons = () => {
    const pages: (number | string)[] = [];
    const maxToShow = 5;

    if (totalPages <= maxToShow) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      const left = Math.max(2, currentPage - 1);
      const right = Math.min(totalPages - 1, currentPage + 1);

      pages.push(1);
      if (left > 2) pages.push("...");
      for (let p = left; p <= right; p++) pages.push(p);
      if (right < totalPages - 1) pages.push("...");
      pages.push(totalPages);
    }

    return pages.map((p, idx) =>
      typeof p === "string" ? (
        <span
          key={`ellipsis-${idx}`}
          className="px-2 text-gray-500 select-none"
        >
          …
        </span>
      ) : (
        <BaseButton
          key={p}
          variant="outline"
          size="sm"
          onClick={() => onPageChange(p)}
          className={currentPage === p ? "bg-orange-500 text-white" : ""}
        >
          {p}
        </BaseButton>
      )
    );
  };

  return (
    <div className="flex items-center justify-between">
      <p className="text-sm text-gray-700">
        Showing {startIndex} to {endIndex} of {totalItems} entries
      </p>

      <div className="flex items-center gap-2">
        {/* Prev */}
        <BaseButton
          variant="outline"
          size="sm"
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className="px-2"
          aria-label="Previous page"
        >
          <ChevronLeft className="w-4 h-4" />
        </BaseButton>

        {/* Page numbers with ellipsis */}
        {renderPageButtons()}

        {/* Next */}
        <BaseButton
          variant="outline"
          size="sm"
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className="px-2"
          aria-label="Next page"
        >
          <ChevronRight className="w-4 h-4" />
        </BaseButton>

        {/* Page size selector: "10 / Page" */}
        <div className="ml-2">
          <BaseSelect
            value={String(pageSize)}
            onValueChange={(v) => onPageSizeChange(Number(v))}
          >
            <BaseSelectTrigger className="w-[110px]">
              <BaseSelectValue placeholder={`${pageSize} / Page`} />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {pageSizeOptions.map((opt) => (
                <BaseSelectItem key={opt} value={String(opt)}>
                  {opt} / Page
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Go to */}
        <div className="flex items-center gap-2 ml-2">
          <span className="text-sm text-gray-700 text-nowrap">Go to</span>
          <BaseInput
            type="number"
            min={1}
            max={totalPages}
            value={goto}
            onChange={(e) => setGoto(e.target.value)}
            className="w-16 h-9"
          />
          <BaseButton
            variant="outline"
            size="sm"
            onClick={() => {
              const n = Number(goto);
              if (!Number.isNaN(n)) {
                const clamped = Math.max(1, Math.min(totalPages, n));
                onPageChange(clamped);
              }
            }}
          >
            Go
          </BaseButton>
        </div>
      </div>
    </div>
  );
};

export default Pagination;
