"use client";
import React, { useState } from "react";
import QuestionBankTableHeaderSearch from "./search";
import QuestionBankTableHeaderFilter from "./filter";
import QuestionBankFilterInput from "./filter-input";

const QuestionBankTableHeader = () => {
  const [filterOpen, setFilterOpen] = useState(false);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <QuestionBankTableHeaderSearch />
        <QuestionBankTableHeaderFilter
          filterOpen={filterOpen}
          setFilterOpen={setFilterOpen}
        />
      </div>
      {filterOpen && <QuestionBankFilterInput />}
    </div>
  );
};

export default QuestionBankTableHeader;
