"use client";

import { useState } from "react";
import {
  BaseDialog,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
} from "@/components/atoms/dialog";
import { BaseButton } from "@/components/atoms/button";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import {
  BaseTable,
  BaseTableBody,
  BaseTableCell,
  BaseTableHead,
  BaseTableHeader,
  BaseTableRow,
} from "@/components/atoms/table";
import PillDropdown from "@/components/atoms/dropdown/pill-dropdown";
import Pagination from "../common/pagination";

interface QuestionTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddQuestions: (questions: any[]) => void;
}

// Dummy question template data
const questionTemplates = [
  { id: "template1", name: "Basic Math Template", questionCount: 25 },
  { id: "template2", name: "Advanced Science Template", questionCount: 30 },
  { id: "template3", name: "Language Arts Template", questionCount: 20 },
  { id: "template4", name: "History Template", questionCount: 35 },
];

// Dummy questions for selected template
const templateQuestions = [
  {
    id: "12345",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12346",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12347",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12348",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12349",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12350",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12351",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12352",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12353",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
  {
    id: "12354",
    category: "2 Category",
    level: "4 Levels",
    questionType: "Pilihan Ganda",
    question:
      "Surveyori Apa bertanya ke setiap perumahan bahwa tidak mengenal customer. Informasi...",
    optionA: "Informasi Negatif",
    optionB: "Informasi Positif",
    optionC: "Informasi Tambahan",
  },
];

const QuestionTemplateModal = ({
  isOpen,
  onClose,
  onAddQuestions,
}: QuestionTemplateModalProps) => {
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [showPreview, setShowPreview] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const handleSelectTemplate = () => {
    if (selectedTemplate) {
      setShowPreview(true);
    }
  };

  const handleAddQuestion = () => {
    onAddQuestions(templateQuestions);
    onClose();
  };

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentQuestions = templateQuestions.slice(startIndex, endIndex);

  const [pageSize, setPageSize] = useState(10);
  const pageSizeOptions = [10, 20, 50, 100];

  return (
    <BaseDialog open={isOpen} onOpenChange={onClose}>
      <BaseDialogContent className="sm:max-w-7xl sm:w-[80vw] max-h-[90vh] overflow-auto p-5 flex flex-col">
        <BaseDialogHeader className="flex flex-col items-start justify-between space-y-0">
          <BaseDialogTitle className="text-base font-medium text-comp-content-primary">
            Add Question from Question Template
          </BaseDialogTitle>
          <hr className=" border-gray-200 -mx-5 w-[calc(100%+40px)]" />
        </BaseDialogHeader>

        <div className="space-y-4">
          {/* Template Selection */}
          <div className="flex items-center gap-4">
            <div className="space-y-2 w-full">
              <label className="text-sm font-medium">Question Template</label>
              <BaseSelect
                value={selectedTemplate}
                onValueChange={setSelectedTemplate}
              >
                <BaseSelectTrigger className="w-full">
                  <BaseSelectValue placeholder="Search and Select Question" />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  {questionTemplates.map((template) => (
                    <BaseSelectItem key={template.id} value={template.id}>
                      {template.name} ({template.questionCount} questions)
                    </BaseSelectItem>
                  ))}
                </BaseSelectContent>
              </BaseSelect>
            </div>
            <BaseButton
              onClick={handleSelectTemplate}
              className="bg-orange-500 hover:bg-orange-600 mt-6"
              disabled={!selectedTemplate}
            >
              Select Question Template
            </BaseButton>
          </div>

          {/* Table Preview */}
          {showPreview && (
            <div className="border rounded-lg overflow-auto w-full">
              <div className="max-h-[500px] overflow-auto w-full">
                <BaseTable className="min-w-full">
                  <BaseTableHeader>
                    <BaseTableRow className="bg-gray-50">
                      <BaseTableHead className="w-20 sticky px-3 py-4 top-0 bg-gray-50 z-10">
                        Question ID
                      </BaseTableHead>
                      <BaseTableHead className="w-24 sticky px-3 py-4 top-0 bg-gray-50 z-10">
                        Category
                      </BaseTableHead>
                      <BaseTableHead className="w-20 sticky px-3 py-4 top-0 bg-gray-50 z-10">
                        Level
                      </BaseTableHead>
                      <BaseTableHead className="w-28 sticky px-3 py-4 top-0 bg-gray-50 z-10">
                        Question Type
                      </BaseTableHead>
                      <BaseTableHead className="min-w-[200px] max-w-[400px] w-full bg-gray-50 sticky px-3 py-4 top-0 z-10">
                        Question
                      </BaseTableHead>
                      <BaseTableHead className="w-[200px] sticky px-3 py-4 top-0 bg-gray-50 z-10">
                        Option A
                      </BaseTableHead>
                      <BaseTableHead className="w-[200px] sticky px-3 py-4 top-0 bg-gray-50 z-10">
                        Option B
                      </BaseTableHead>
                      <BaseTableHead className="w-[200px] sticky px-3 py-4 top-0 bg-gray-50 z-10">
                        Option C
                      </BaseTableHead>
                    </BaseTableRow>
                  </BaseTableHeader>
                  <BaseTableBody>
                    {currentQuestions.map((question) => (
                      <BaseTableRow key={question.id}>
                        <BaseTableCell className="font-medium">
                          {question.id}
                        </BaseTableCell>
                        <BaseTableCell className="py-4 px-3">
                          <PillDropdown
                            selected={"2 Sections"}
                            options={[
                              { label: "Sub-Module 1", value: "1" },
                              { label: "Sub-Module 2", value: "2" },
                              { label: "Sub-Module 3", value: "3" },
                            ]}
                            onSelect={(option) => console.log(option)}
                          />
                        </BaseTableCell>
                        <BaseTableCell className="py-4 px-3">
                          <PillDropdown
                            selected={"4 Level"}
                            options={[
                              { label: "Sub-Module 1", value: "1" },
                              { label: "Sub-Module 2", value: "2" },
                              { label: "Sub-Module 3", value: "3" },
                            ]}
                            onSelect={(option) => console.log(option)}
                          />
                        </BaseTableCell>
                        <BaseTableCell className="py-4 px-3">
                          {question.questionType}
                        </BaseTableCell>
                        <BaseTableCell
                          className="min-w-[200px] max-w-[400px] w-full py-4 px-3"
                          title={question.question}
                        >
                          <div className="line-clamp-2 text-wrap">
                            {question.question}
                          </div>
                        </BaseTableCell>
                        <BaseTableCell
                          className="w-[200px] truncate py-4 px-3"
                          title={question.optionA}
                        >
                          {question.optionA}
                        </BaseTableCell>
                        <BaseTableCell
                          className="w-[200px] truncate py-4 px-3"
                          title={question.optionB}
                        >
                          {question.optionB}
                        </BaseTableCell>
                        <BaseTableCell
                          className="w-[200px] truncate py-4 px-3"
                          title={question.optionC}
                        >
                          {question.optionC}
                        </BaseTableCell>
                      </BaseTableRow>
                    ))}
                  </BaseTableBody>
                </BaseTable>
              </div>
            </div>
          )}
        </div>

        <Pagination
          totalItems={templateQuestions.length}
          currentPage={currentPage}
          pageSize={pageSize}
          pageSizeOptions={pageSizeOptions}
          onPageChange={(page) => setCurrentPage(page)}
          onPageSizeChange={(size) => {
            setPageSize(size);
            setCurrentPage(1);
          }}
        />

        {/* Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <BaseButton variant="outline" onClick={onClose}>
            Cancel
          </BaseButton>
          <BaseButton
            onClick={handleAddQuestion}
            className="bg-orange-500 hover:bg-orange-600"
            disabled={!showPreview}
          >
            Add Question
          </BaseButton>
        </div>
      </BaseDialogContent>
    </BaseDialog>
  );
};

export default QuestionTemplateModal;
