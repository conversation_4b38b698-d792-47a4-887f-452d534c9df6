"use client";

import { useMemo } from "react";
import { DataTable } from "../../global/table";
import ManageFaqTableHeader from "./table-header";

import { useGetListFaqQuery } from "@/services/query/admin/manage-faq";
import { Skeleton } from "@/components/ui/skeleton";
import { useShallow } from "zustand/react/shallow";
import { useManageFaqModal } from "@/store/admin/manage-faq/modal";
import { getColumnsManageFaq } from "./columns";
import { useManageFaqQueryStore } from "@/store/admin/manage-faq/query";

const ManageFaqTable = () => {
  const faqQuery = useManageFaqQueryStore((state) => state.faqQuery);
  console.log("faqQuery", faqQuery);
  const { data, isPending } = useGetListFaqQuery({
    ...faqQuery,
    search_by: faqQuery.search_by,
    search: faqQuery.search,
  });
  const { setOpenAddFaq, setOpenedFaqId } = useManageFaqModal(
    useShallow(({ setOpenAddFaq, setOpenedFaqId }) => ({
      setOpenAddFaq,
      setOpenedFaqId,
    }))
  );
  const columns = useMemo(
    () =>
      getColumnsManageFaq({
        onEdit: (id) => {
          setOpenedFaqId(id);
          setOpenAddFaq(true);
        },
        onDelete: (id) => console.log(id),
      }),
    []
  );

  if (isPending) {
    return <Skeleton className="h-11" />;
  }

  return (
    <div className="flex flex-col gap-4">
      <ManageFaqTableHeader />
      <DataTable
        containerClassName="max-h-[calc(100vh-280px)] overflow-y-auto"
        columns={columns}
        data={data?.data ?? []}
        pagination={data?.pagination}
      />
    </div>
  );
};

export default ManageFaqTable;
