"use client";

import FilterButton from "@/components/atoms/filter-button";
import { useManageCertificateFilterStore } from "@/store/admin/manage-certificate/filter";
import { useShallow } from "zustand/react/shallow";

interface Props {
  filterOpen: boolean;
  setFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const ManageCertificateTableHeaderFilter = ({
  filterOpen,
  setFilterOpen,
}: Readonly<Props>) => {
  const { openFilter, setOpenFilter } = useManageCertificateFilterStore(
    useShallow((state) => ({
      openFilter: state.openFilter,
      setOpenFilter: state.setOpenFilter,
    }))
  );

  const handleFilterToggle = () => {
    const newState = !filterOpen;
    setFilterOpen(newState);
    setOpenFilter(newState);
  };

  return (
    <div className="flex items-center justify-end gap-3">
      <FilterButton
        active={filterOpen}
        onClick={handleFilterToggle}
        className="h-12"
      />
    </div>
  );
};

export default ManageCertificateTableHeaderFilter;
