import React from "react";
import { UseFormReturn } from "react-hook-form";
import { BaseButton } from "@/components/atoms/button";
import { BaseLabel } from "@/components/atoms/label";
import StepBackButton from "./step-back-button";
import OtpInput from "./otp-input";
import { IconCopy } from "@/assets/icons/IconCopy";
import { useMediaQuery } from "@/hooks";
import { Step4Values, IEnrollMfaResponse } from "@/hooks/useLoginFlow";
import Spinner from "@/components/atoms/spinner";
import Image from "next/image";

interface LoginStep4Props {
  form: UseFormReturn<Step4Values>;
  onSubmit: (data: Step4Values) => Promise<void>;
  onBack: () => void;
  onCopySecret: () => Promise<void>;
  onOpenInstructions: () => void;
  mfaData: IEnrollMfaResponse | null;
  isLoading: boolean;
}

const LoginStep4: React.FC<LoginStep4Props> = ({
  form,
  onSubmit,
  onBack,
  onCopySecret,
  onOpenInstructions,
  mfaData,
  isLoading,
}) => {
  const isMobile = useMediaQuery("(max-width: 767px)");

  return (
    <>
      <StepBackButton onBack={onBack} />
      <p className="text-lg leading-[140%] font-medium text-[#3C3C3C] mb-6">
        2 Factor Authenticator
      </p>
      <div className="flex flex-col gap-3 mb-6">
        <div className="w-[158px] h-[158px] p-1 border border-[#DEDEDE] self-center bg-white flex items-center justify-center">
          {mfaData?.qrCodeDataBin ? (
            <Image
              src={`data:image/png;base64,${mfaData?.qrCodeDataBin}`}
              alt="QR Code"
              width={148}
              height={148}
              unoptimized
            />
          ) : (
            <span className="text-xs text-[#767676]">Generating…</span>
          )}
        </div>
        <div className="flex flex-col">
          <p className="text-sm text-[#3C3C3C] mb-1">Secret Key</p>
          <div className="flex py-3 lg:py-[14.5px] flex-row gap-4 justify-center items-center bg-[#F6F6F6]">
            <p className="text-xs lg:text-[15px] leading-[140%] text-[#3C3C3C] font-medium">
              {mfaData?.setupKey || "—"}
            </p>
            <button
              type="button"
              className="cursor-pointer"
              onClick={onCopySecret}
              disabled={!mfaData?.setupKey}
            >
              <IconCopy size={isMobile ? 14 : 16} />
            </button>
          </div>
        </div>
        <div className="text-xs">
          <p className="text-[#3C3C3C]">
            Pindai QR code ini menggunakan Microsoft Authenticator.{" "}
            <button
              type="button"
              className="text-[#0095FF] font-medium underline cursor-pointer"
              onClick={onOpenInstructions}
            >
              Lihat Instruksi
            </button>
          </p>
        </div>
      </div>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className={`${form.formState.errors.otpMfa ? "mb-5" : "mb-6"}`}>
          <BaseLabel
            htmlFor="otpMfa"
            className="text-xs md:text-sm font-medium text-[#3C3C3C] mb-1"
          >
            Authentication Code
          </BaseLabel>
          <OtpInput
            value={form.watch("otpMfa")}
            onChange={(val) => form.setValue("otpMfa", val)}
            error={form.formState.errors.otpMfa?.message}
          />
          {form.formState.errors.otpMfa && (
            <p className="text-[#EA2B1F] text-xs mt-1">
              {form.formState.errors.otpMfa.message}
            </p>
          )}
        </div>

        <div className="flex flex-col gap-4">
          <BaseButton
            type="submit"
            className="w-full bg-[#F7941E] text-white h-[44px] md:h-[48px] rounded-[8px] hover:bg-[#F7941E] hover:opacity-80 cursor-pointer"
            disabled={isLoading}
          >
            {isLoading ? <Spinner /> : "Verify OTP Code"}
          </BaseButton>
        </div>
      </form>
    </>
  );
};

export default LoginStep4;
