import { IGetListMaterialResponse } from "@/interfaces/admin/manage-material/list";
import { useGetLearningMaterialUrlQuery } from "@/services/query/admin/manage-material";

interface DocumentPreviewProps {
  material: IGetListMaterialResponse | null;
}
export const DocumentPreview = ({ material }: DocumentPreviewProps) => {
  const { data: learningMaterialUrl, isPending } =
    useGetLearningMaterialUrlQuery({
      url: material?.link || "",
    });

  return (
    <div className="h-full w-full">
      <iframe
        src={`${learningMaterialUrl?.data?.url}`}
        className="w-full h-full border-0"
        title="PDF Preview"
      />
    </div>
  );
};

export default DocumentPreview;
