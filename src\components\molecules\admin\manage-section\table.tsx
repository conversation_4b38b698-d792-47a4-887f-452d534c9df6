"use client";

import React from "react";
import { DataTable } from "../../global/table";
import { Section } from "@/interfaces/admin/manage-section/list";
import { getSectionColumns } from "./column";
import ManageSectionTableHeader from "./table-header";
import ConfirmationModal from "../../modal/confirmation-modal";
import { IGlobalPaginationDto } from "@/interfaces/global/pagination";

interface IModalData {
  isOpen: boolean;
  data: Section | null;
}

// Dummy pagination data
const DUMMY_PAGINATION: IGlobalPaginationDto = {
  current_page: 1,
  total_page: 1,
  total_data: 1,
  next: null,
  prev: null,
};

// Dummy data for the sections table
const dummySections: Section[] = [
  {
    sectionId: 12345,
    sectionName: "Section A Learning Development",
    sectionType: "Video",
    duration: "00 hrs 11 mnt 00 sec",
    technicalCompetencies: null,
    softCompetencies: null,
    attachedSubModule: "5 Sub-Module",
    lastUpdated: "2025-01-10T09:30:00.000Z",
    updatedBy: "Admin User",
  },
  {
    sectionId: 12346,
    sectionName: "Section B Safety Training",
    sectionType: "Document",
    duration: "00 hrs 15 mnt 00 sec",
    technicalCompetencies: "Safety Protocols",
    softCompetencies: "Communication",
    attachedSubModule: "3 Sub-Module",
    lastUpdated: "2025-01-09T14:20:00.000Z",
    updatedBy: "Safety Manager",
  },
  {
    sectionId: 12347,
    sectionName: "Section C Assessment Quiz",
    sectionType: "Quiz",
    duration: "00 hrs 30 mnt 00 sec",
    technicalCompetencies: "Technical Knowledge",
    softCompetencies: null,
    attachedSubModule: "1 Sub-Module",
    lastUpdated: "2025-01-08T10:15:00.000Z",
    updatedBy: "Quiz Admin",
  },
  {
    sectionId: 12348,
    sectionName: "Section D Compliance Training",
    sectionType: "Video",
    duration: "00 hrs 45 mnt 00 sec",
    technicalCompetencies: "Compliance Standards",
    softCompetencies: "Ethics",
    attachedSubModule: "7 Sub-Module",
    lastUpdated: "2025-01-07T16:30:00.000Z",
    updatedBy: "Compliance Officer",
  },
  {
    sectionId: 12349,
    sectionName: "Section E Soft Skills Development",
    sectionType: "Document",
    duration: "00 hrs 20 mnt 00 sec",
    technicalCompetencies: null,
    softCompetencies: "Leadership, Teamwork",
    attachedSubModule: "4 Sub-Module",
    lastUpdated: "2025-01-06T11:45:00.000Z",
    updatedBy: "HR Manager",
  },
  {
    sectionId: 12350,
    sectionName: "Section F Technical Assessment",
    sectionType: "Quiz",
    duration: "01 hrs 00 mnt 00 sec",
    technicalCompetencies: "Advanced Technical Skills",
    softCompetencies: "Problem Solving",
    attachedSubModule: "2 Sub-Module",
    lastUpdated: "2025-01-05T13:20:00.000Z",
    updatedBy: "Technical Lead",
  },
  {
    sectionId: 12351,
    sectionName: "Section G Introduction Video",
    sectionType: "Video",
    duration: "00 hrs 08 mnt 30 sec",
    technicalCompetencies: null,
    softCompetencies: null,
    attachedSubModule: "1 Sub-Module",
    lastUpdated: "2025-01-04T09:10:00.000Z",
    updatedBy: "Content Creator",
  },
  {
    sectionId: 12352,
    sectionName: "Section H Final Evaluation",
    sectionType: "Quiz",
    duration: "01 hrs 30 mnt 00 sec",
    technicalCompetencies: "Comprehensive Assessment",
    softCompetencies: "Critical Thinking",
    attachedSubModule: "8 Sub-Module",
    lastUpdated: "2025-01-03T15:45:00.000Z",
    updatedBy: "Evaluation Team",
  },
  {
    sectionId: 12353,
    sectionName: "Section I Resource Materials",
    sectionType: "Document",
    duration: "00 hrs 25 mnt 00 sec",
    technicalCompetencies: "Reference Materials",
    softCompetencies: null,
    attachedSubModule: "6 Sub-Module",
    lastUpdated: "2025-01-02T12:30:00.000Z",
    updatedBy: "Resource Manager",
  },
  {
    sectionId: 12354,
    sectionName: "Section J Interactive Workshop",
    sectionType: "Video",
    duration: "02 hrs 00 mnt 00 sec",
    technicalCompetencies: "Hands-on Practice",
    softCompetencies: "Collaboration",
    attachedSubModule: "10 Sub-Module",
    lastUpdated: "2025-01-01T08:00:00.000Z",
    updatedBy: "Workshop Facilitator",
  },
];

const ManageSectionTable = () => {
  const [deleteModal, setDeleteModal] = React.useState<IModalData>({
    isOpen: false,
    data: null,
  });

  const columns = React.useMemo(
    () =>
      getSectionColumns({
        onEdit: (section) => {
          console.log("Edit section:", section);
          // TODO: Implement edit functionality
        },
        onDelete: (section) => {
          setDeleteModal({ isOpen: true, data: section });
        },
      }),
    []
  );

  const handleDeleteConfirm = () => {
    console.log("Delete section:", deleteModal.data);
    // TODO: Implement delete functionality
    setDeleteModal({ isOpen: false, data: null });
  };

  return (
    <div className="flex flex-col gap-4 h-full">
      <ConfirmationModal
        isOpen={deleteModal.isOpen}
        onOpenChange={(open) => setDeleteModal({ isOpen: open, data: null })}
        onConfirm={handleDeleteConfirm}
        title="Delete Section?"
        description={`Are you sure you want to delete "${deleteModal.data?.sectionName}"? This action cannot be undone.`}
      />
      <ManageSectionTableHeader />
      <DataTable
        columns={columns}
        data={dummySections}
        pagination={DUMMY_PAGINATION}
        onPageChange={(page) => console.log(page)}
      />
    </div>
  );
};

export default ManageSectionTable;
