"use client";
import React from "react";
import ManageMaterialTableHeaderSearch from "./search";
import ManageMaterialTableHeaderFilter from "./filter";
import ManageMaterialFilterInput from "./filter-input";
import { useState } from "react";
import { useManageMaterialTabStore } from "@/store/admin/manage-material/tab";
import { useShallow } from "zustand/react/shallow";
import { useManageMaterialQueryStore } from "@/store/admin/manage-material/query";

const ManageMaterialTableHeader = () => {
  const [filterOpen, setFilterOpen] = useState(false);
  const { activeTab } = useManageMaterialTabStore(
    useShallow(({ activeTab }) => ({ activeTab }))
  );
  const { materialQuery, setMaterialQuery } = useManageMaterialQueryStore(
    useShallow((state) => ({
      materialQuery: state.materialQuery,
      setMaterialQuery: state.setMaterialQuery,
    }))
  );

  const onFilterChange = (filters: { category?: string; level?: string }) => {
    const category = filters.category === "all" ? undefined : filters.category;
    const level = filters.level === "all" ? undefined : filters.level;
    setMaterialQuery({
      ...materialQuery,
      category_id: category,
      level_id: level,
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex justify-between items-center">
        <ManageMaterialTableHeaderSearch />
        <ManageMaterialTableHeaderFilter
          filterOpen={filterOpen}
          setFilterOpen={setFilterOpen}
        />
      </div>
      {filterOpen && (
        <ManageMaterialFilterInput
          activeTab={activeTab}
          onFilterChange={onFilterChange}
        />
      )}
    </div>
  );
};

export default ManageMaterialTableHeader;
