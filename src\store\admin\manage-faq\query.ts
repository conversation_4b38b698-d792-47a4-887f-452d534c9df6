import { IGetListFaqParams } from "@/interfaces/admin/manage-faq/list";
import { create } from "zustand";

interface IManageFaqQuery {
  faqQuery: IGetListFaqParams;
  setFaqQuery: (query: Partial<IGetListFaqParams>) => void;
}

export const useManageFaqQueryStore = create<IManageFaqQuery>()((set, get) => ({
  faqQuery: { page: 1, limit: 10, search: "", search_by: "" },
  setFaqQuery: (query) =>
    set({ faqQuery: { ...(get().faqQuery as any), ...query } }),
}));
