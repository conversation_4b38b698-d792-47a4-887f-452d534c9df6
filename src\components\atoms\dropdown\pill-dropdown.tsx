import { IMaterial } from "@/interfaces/admin/manage-material/list";
import { ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

type Option = {
  value: string;
  label: string;
};

type Props = {
  selected: string;
  options: Array<Option>;
  onSelect: (option: Option) => void;
};

const PillDropdown = ({ selected, options, onSelect }: Props) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center gap-1 cursor-pointer bg-[#DEDEDE] px-2 py-1 text-xs text-comp-content-primary max-w-max rounded-full">
          <span>{selected}</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        {options.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onSelect={() => onSelect(option)}
          >
            {option.value}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default PillDropdown;
