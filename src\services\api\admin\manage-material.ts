"use server";

import {
  IGetListMaterialQuery,
  IGetListMaterialResponse,
} from "@/interfaces/admin/manage-material/list";
import { IGlobalResponseDto } from "@/interfaces/global/response";
import { api } from "@/services/satellite";
import { handleAxiosError } from "@/utils/common/axios";

export const apiGetListMaterials = async (params?: IGetListMaterialQuery) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<IGetListMaterialResponse[]>
    >("cms/admin/learning/material/list", { params });

    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};

export const apiGetLearningMaterialUrl = async (params: { url: string }) => {
  try {
    const response = await api.get<
      IGlobalResponseDto<{ filename: string; url: string }>
    >("cms/admin/learning/material/url", { params });
    return response.data;
  } catch (error) {
    console.log(error);
    throw handleAxiosError(error);
  }
};
