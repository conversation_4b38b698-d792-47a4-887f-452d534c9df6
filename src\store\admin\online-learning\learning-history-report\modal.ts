import { create } from 'zustand';

interface ILearningHistoryReportDetailModal {
  openedId: string;
  setOpenedId: (id: string) => void;
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
}

export const useLearningHistoryReportDetailModal =
  create<ILearningHistoryReportDetailModal>()((set) => ({
    openedId: '',
    setOpenedId: (id: string) => set({ openedId: id }),
    openModal: false,
    setOpenModal: (open: boolean) => set({ openModal: open }),
  }));
