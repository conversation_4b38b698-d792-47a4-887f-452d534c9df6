"use client";

import { BaseLabel } from "@/components/atoms/label";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { BaseButton } from "@/components/atoms/button";
import { OrangeCheckbox } from "@/components/atoms/checkbox/orange-checkbox";
import { useState } from "react";

interface IFilter {
  sectionType: string[];
}

const sectionTypeOptions = [
  { value: "Video", label: "Video" },
  { value: "Audio", label: "Audio" },
  { value: "PDF", label: "PDF" },
  { value: "Quiz", label: "Quiz" },
  { value: "Pre-test", label: "Pre-test" },
  { value: "Post-test", label: "Post-test" },
];

const ManageSectionFilterInput = () => {
  const [filter, setFilter] = useState<IFilter>({
    sectionType: [],
  });

  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleSectionTypeChange = (value: string, checked: boolean) => {
    setFilter((prev) => ({
      ...prev,
      sectionType: checked
        ? [...prev.sectionType, value]
        : prev.sectionType.filter((type) => type !== value),
    }));
  };

  const handleApply = () => {
    console.log("Applying filters:", filter);
    // TODO: Apply filters to the table
  };

  const handleReset = () => {
    setFilter({
      sectionType: [],
    });
  };

  return (
    <div className="flex flex-col gap-4 w-full bg-white rounded-lg p-3">
      <div className="flex items-center justify-between">
        <span className="font-semibold">Filter</span>
        <div className="flex gap-3">
          <BaseButton
            variant="outline"
            className="text-red-600 border-red-600 hover:text-red-600 h-9"
            onClick={handleReset}
          >
            Reset
          </BaseButton>
          <BaseButton
            className="h-9 bg-orange-500 hover:bg-orange-600"
            onClick={handleApply}
          >
            Apply
          </BaseButton>
        </div>
      </div>
      <BaseSeparator />
      <div className="grid grid-cols-1 gap-4">
        {/* Section Type Filter */}
        <div className="flex flex-col gap-2 max-w-[280px]">
          <BaseLabel className="text-sm font-medium">Question Type</BaseLabel>
          <BaseSelect
            value=""
            onValueChange={() => setDropdownOpen(!dropdownOpen)}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue
                placeholder={
                  filter.sectionType.length > 0
                    ? `${filter.sectionType.length} selected`
                    : "Select question type"
                }
              />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <div className="p-2 space-y-2">
                {sectionTypeOptions.map((option) => (
                  <div
                    key={option.value}
                    className="flex items-center space-x-2"
                  >
                    <OrangeCheckbox
                      id={option.value}
                      checked={filter.sectionType.includes(option.value)}
                      onCheckedChange={(checked) =>
                        handleSectionTypeChange(
                          option.value,
                          checked as boolean
                        )
                      }
                    />
                    <BaseLabel
                      htmlFor={option.value}
                      className="text-sm font-normal"
                    >
                      {option.label}
                    </BaseLabel>
                  </div>
                ))}
                <div className="flex gap-2 pt-2 border-t">
                  <BaseButton
                    variant="outline"
                    size="sm"
                    onClick={handleReset}
                    className="text-orange-600 border-orange-600 hover:text-orange-600"
                  >
                    Reset
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    onClick={() => setDropdownOpen(false)}
                    className="bg-orange-500 hover:bg-orange-600"
                  >
                    Ok
                  </BaseButton>
                </div>
              </div>
            </BaseSelectContent>
          </BaseSelect>
        </div>
      </div>
    </div>
  );
};

export default ManageSectionFilterInput;
