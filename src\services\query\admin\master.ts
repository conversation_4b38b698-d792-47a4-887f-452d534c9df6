import { IGetCategoryListQuery } from "@/interfaces/admin/master/category";
import { IGetStartingLevelListQuery } from "@/interfaces/admin/master/starting-level";
import {
  apiGetCategoryList,
  apiGetStartingLevelList,
} from "@/services/api/master/starting-level";
import { useQuery } from "@tanstack/react-query";

export const useGetStartingLevelListQuery = (
  params?: IGetStartingLevelListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["starting-level", params],
    queryFn: async () => {
      return await apiGetStartingLevelList(params);
    },
    enabled,
  });
};

export const useGetCategoryListQuery = (
  params?: IGetCategoryListQuery,
  enabled = true
) => {
  return useQuery({
    queryKey: ["category", params],
    queryFn: async () => {
      return await apiGetCategoryList(params);
    },
    enabled,
  });
};
