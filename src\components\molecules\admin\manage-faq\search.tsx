import { BaseInput } from "@/components/atoms/input";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { BaseSeparator } from "@/components/atoms/separator";
import { Search } from "lucide-react";
import React from "react";
import { useManageFaqQueryStore } from "@/store/admin/manage-faq/query";
import { debounce } from "lodash";

const ManageFaqTableHeaderSearch = () => {
  const { faqQuery, setFaqQuery } = useManageFaqQueryStore();

  const handleSearchChange = debounce(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFaqQuery({ search: e.target.value });
    },
    500
  );

  return (
    <div className="text-[#3C3C3C] font-semibold rounded-lg flex items-center gap-1 relative w-[30%] bg-white px-3">
      <BaseSelect
        value={faqQuery.search_by}
        onValueChange={(value) => setFaqQuery({ search_by: value })}
      >
        <BaseSelectTrigger className="w-32 border-none px-1">
          <BaseSelectValue placeholder="Search By" />
        </BaseSelectTrigger>
        <BaseSelectContent>
          <BaseSelectItem value="question">Question</BaseSelectItem>
          <BaseSelectItem value="answer">Answer</BaseSelectItem>
        </BaseSelectContent>
      </BaseSelect>
      <BaseSeparator orientation="vertical" />
      <BaseInput
        className="border-none h-12 focus-visible:border-none focus-visible:ring-0"
        onChange={handleSearchChange}
      />
      <Search size={24} />
      <BaseSeparator orientation="vertical" className="bg-red-500 w-1" />
    </div>
  );
};

export default ManageFaqTableHeaderSearch;
