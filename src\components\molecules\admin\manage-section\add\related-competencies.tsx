"use client";

import React from "react";
import { Control, FieldErrors } from "react-hook-form";
import { AddSectionFormData } from "@/components/organisms/admin/manage-section/add-new-section";
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from "@/components/atoms/select";
import { Controller } from "react-hook-form";
import { cn } from "@/lib/utils";

interface RelatedCompetenciesProps {
  control: Control<AddSectionFormData>;
  errors: FieldErrors<AddSectionFormData>;
}

const RelatedCompetencies = ({ control, errors }: RelatedCompetenciesProps) => {
  return (
    <div className="bg-white">
      <h2 className="text-sm font-medium text-comp-content-primary mb-3">
        Related Competencies
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        {/* Technical Competencies */}
        <div>
          <label className="block text-xs font-medium text-comp-content-primary mb-1">
            Technical Competencies
          </label>
          <Controller
            name="technicalCompetencies"
            control={control}
            render={({ field }) => (
              <BaseSelect
                value={field.value?.join(",") || ""}
                onValueChange={(value) => field.onChange(value ? [value] : [])}
              >
                <BaseSelectTrigger
                  className={cn(
                    "w-full",
                    errors.technicalCompetencies ? "border-red-500" : ""
                  )}
                >
                  <BaseSelectValue placeholder="Select technical competencies" />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  <BaseSelectItem value="programming">
                    Programming
                  </BaseSelectItem>
                  <BaseSelectItem value="database">
                    Database Management
                  </BaseSelectItem>
                  <BaseSelectItem value="networking">Networking</BaseSelectItem>
                  <BaseSelectItem value="security">
                    Cybersecurity
                  </BaseSelectItem>
                  <BaseSelectItem value="cloud">Cloud Computing</BaseSelectItem>
                  <BaseSelectItem value="devops">DevOps</BaseSelectItem>
                </BaseSelectContent>
              </BaseSelect>
            )}
          />
          {errors.technicalCompetencies && (
            <p className="text-red-500 text-sm mt-1">
              {errors.technicalCompetencies.message}
            </p>
          )}
        </div>

        {/* Soft Competencies */}
        <div>
          <label className="block text-xs font-medium text-comp-content-primary mb-1">
            Soft Competencies
          </label>
          <Controller
            name="softCompetencies"
            control={control}
            render={({ field }) => (
              <BaseSelect
                value={field.value?.join(",") || ""}
                onValueChange={(value) => field.onChange(value ? [value] : [])}
              >
                <BaseSelectTrigger
                  className={cn(
                    "w-full",
                    errors.softCompetencies ? "border-red-500" : ""
                  )}
                >
                  <BaseSelectValue placeholder="Select soft competencies" />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  <BaseSelectItem value="communication">
                    Communication
                  </BaseSelectItem>
                  <BaseSelectItem value="leadership">Leadership</BaseSelectItem>
                  <BaseSelectItem value="teamwork">Teamwork</BaseSelectItem>
                  <BaseSelectItem value="problem-solving">
                    Problem Solving
                  </BaseSelectItem>
                  <BaseSelectItem value="time-management">
                    Time Management
                  </BaseSelectItem>
                  <BaseSelectItem value="adaptability">
                    Adaptability
                  </BaseSelectItem>
                </BaseSelectContent>
              </BaseSelect>
            )}
          />
          {errors.softCompetencies && (
            <p className="text-red-500 text-sm mt-1">
              {errors.softCompetencies.message}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default RelatedCompetencies;
